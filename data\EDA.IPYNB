import polars as pl
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Any, Optional
import warnings
warnings.filterwarnings('ignore')

class PolarEDA:
    """
    Comprehensive EDA class for large datasets using Polars
    Optimized for datasets with dimensions like 200x50,000
    """
    
    def __init__(self, df: pl.DataFrame):
        self.df = df
        self.numeric_cols = []
        self.categorical_cols = []
        self.datetime_cols = []
        self._classify_columns()
        
    def _classify_columns(self):
        """Classify columns by data type"""
        for col in self.df.columns:
            dtype = self.df[col].dtype
            
            if dtype in [pl.Int8, pl.Int16, pl.Int32, pl.Int64, pl.UInt8, pl.UInt16, pl.UInt32, pl.UInt64, pl.Float32, pl.Float64]:
                self.numeric_cols.append(col)
            elif dtype in [pl.Utf8, pl.Categorical]:
                self.categorical_cols.append(col)
            elif dtype in [pl.Date, pl.Datetime]:
                self.datetime_cols.append(col)
    
    def basic_info(self) -> Dict[str, Any]:
        """Get basic dataset information"""
        info = {
            'shape': self.df.shape,
            'columns': len(self.df.columns),
            'memory_usage_mb': self.df.estimated_size() / (1024 * 1024),
            'numeric_columns': len(self.numeric_cols),
            'categorical_columns': len(self.categorical_cols),
            'datetime_columns': len(self.datetime_cols)
        }
        
        print("=" * 50)
        print("DATASET OVERVIEW")
        print("=" * 50)
        for key, value in info.items():
            if key == 'memory_usage_mb':
                print(f"{key.replace('_', ' ').title()}: {value:.2f} MB")
            else:
                print(f"{key.replace('_', ' ').title()}: {value}")
        
        return info
    
    def missing_values_analysis(self) -> pl.DataFrame:
        """Analyze missing values efficiently"""
        print("\n" + "=" * 50)
        print("MISSING VALUES ANALYSIS")
        print("=" * 50)
        
        # Use per-column aliasing for compatibility with Polars
        missing_stats = self.df.select([
            *(pl.col(col).null_count().alias(f"{col}_null_count") for col in self.df.columns),
            *(pl.col(col).count().alias(f"{col}_total_count") for col in self.df.columns)
        ])
        
        # Calculate missing percentages
        missing_df = pl.DataFrame({
            'column': self.df.columns,
            'missing_count': [missing_stats[f"{col}_null_count"][0] for col in self.df.columns],
            'total_count': [missing_stats[f"{col}_total_count"][0] for col in self.df.columns]
        }).with_columns([
            (pl.col('missing_count') / pl.col('total_count') * 100).alias('missing_percentage')
        ]).filter(pl.col('missing_count') > 0).sort('missing_percentage', descending=True)
        
        if missing_df.height > 0:
            print(missing_df)
        else:
            print("No missing values found!")
        
        return missing_df
    
    def numeric_summary(self) -> pl.DataFrame:
        """Generate summary statistics for numeric columns"""
        if not self.numeric_cols:
            print("No numeric columns found!")
            return pl.DataFrame()
        
        print("\n" + "=" * 50)
        print("NUMERIC COLUMNS SUMMARY")
        print("=" * 50)
        
        # Use Polars' efficient describe method
        summary = self.df.select(self.numeric_cols).describe()
        print(summary)
        
        return summary
    
    def categorical_summary(self) -> Dict[str, pl.DataFrame]:
        """Analyze categorical columns"""
        if not self.categorical_cols:
            print("No categorical columns found!")
            return {}
        
        print("\n" + "=" * 50)
        print("CATEGORICAL COLUMNS SUMMARY")
        print("=" * 50)
        
        cat_summaries = {}
        for col in self.categorical_cols:
            print(f"\nColumn: {col}")
            
            # Value counts (top 10 to avoid overwhelming output)
            value_counts = (self.df
                          .group_by(col)
                          .agg(pl.count().alias('count'))
                          .sort('count', descending=True)
                          .head(10))
            
            unique_count = self.df[col].n_unique()
            
            print(f"Unique values: {unique_count}")
            print("Top 10 most frequent values:")
            print(value_counts)
            
            cat_summaries[col] = value_counts
        
        return cat_summaries
    
    def detect_outliers(self, method: str = 'iqr') -> Dict[str, pl.DataFrame]:
        """Detect outliers in numeric columns"""
        if not self.numeric_cols:
            return {}
        
        print("\n" + "=" * 50)
        print(f"OUTLIER DETECTION ({method.upper()} METHOD)")
        print("=" * 50)
        
        outlier_info = {}
        
        for col in self.numeric_cols:
            if method == 'iqr':
                # Calculate quartiles
                q1 = self.df[col].quantile(0.25)
                q3 = self.df[col].quantile(0.75)
                iqr = q3 - q1
                
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr
                
                outliers = self.df.filter(
                    (pl.col(col) < lower_bound) | (pl.col(col) > upper_bound)
                ).select([col])
                
                outlier_count = outliers.height
                outlier_percentage = (outlier_count / self.df.height) * 100
                
                print(f"{col}: {outlier_count} outliers ({outlier_percentage:.2f}%)")
                outlier_info[col] = outliers
        
        return outlier_info
    
    def correlation_analysis(self) -> pl.DataFrame:
        """Calculate correlation matrix for numeric columns"""
        if len(self.numeric_cols) < 2:
            print("Need at least 2 numeric columns for correlation analysis!")
            return pl.DataFrame()
        print("\n" + "=" * 50)
        print("CORRELATION ANALYSIS")
        print("=" * 50)
        
        # Convert to numpy for correlation calculation (Polars doesn't have native corr yet)
        numeric_data = self.df.select(self.numeric_cols).to_numpy()
        corr_matrix = np.corrcoef(numeric_data.T)
        
        # Create correlation DataFrame
        corr_df = pl.DataFrame(
            corr_matrix,
            schema=self.numeric_cols
        ).with_columns(
            pl.Series("column", self.numeric_cols)
        ).select(["column"] + self.numeric_cols)
        
        print(corr_df)
        
        return corr_df
    
    def data_quality_check(self) -> Dict[str, Any]:
        """Comprehensive data quality assessment"""
        print("\n" + "=" * 50)
        print("DATA QUALITY CHECK")
        print("=" * 50)
        
        quality_report = {}
        
        # Check for duplicates
        duplicate_count = self.df.height - self.df.unique().height
        quality_report['duplicates'] = duplicate_count
        
        # Check for constant columns
        constant_cols = []
        for col in self.df.columns:
            if self.df[col].n_unique() == 1:
                constant_cols.append(col)
        quality_report['constant_columns'] = constant_cols
        
        # Check for high cardinality categorical columns
        high_cardinality_cols = []
        for col in self.categorical_cols:
            unique_ratio = self.df[col].n_unique() / self.df.height
            if unique_ratio > 0.5:
                high_cardinality_cols.append(col)
        quality_report['high_cardinality_categorical'] = high_cardinality_cols
        
        print(f"Duplicate rows: {duplicate_count}")
        print(f"Constant columns: {constant_cols}")
        print(f"High cardinality categorical columns: {high_cardinality_cols}")
        
        return quality_report
    
    def generate_plots(self, sample_size: int = 10000):
        """Generate key visualizations (with sampling for large datasets)"""
        print("\n" + "=" * 50)
        print("GENERATING VISUALIZATIONS")
        print("=" * 50)
        
        # Sample data for plotting if dataset is large
        if self.df.height > sample_size:
            plot_df = self.df.sample(sample_size)
            print(f"Using sample of {sample_size} rows for plotting")
        else:
            plot_df = self.df
        
        # Convert to pandas for plotting (matplotlib/seaborn compatibility)
        plot_df_pandas = plot_df.to_pandas()
        
        # 1. Distribution plots for numeric columns
        if self.numeric_cols:
            n_cols = min(4, len(self.numeric_cols))
            n_rows = (len(self.numeric_cols) + n_cols - 1) // n_cols
            
            plt.figure(figsize=(15, 4 * n_rows))
            for i, col in enumerate(self.numeric_cols):
                plt.subplot(n_rows, n_cols, i + 1)
                plt.hist(plot_df_pandas[col].dropna(), bins=50, alpha=0.7)
                plt.title(f'Distribution of {col}')
                plt.xlabel(col)
                plt.ylabel('Frequency')
            plt.tight_layout()
            plt.show()
        
        # 2. Correlation heatmap
        if len(self.numeric_cols) > 1:
            plt.figure(figsize=(12, 8))
            corr_matrix = plot_df_pandas[self.numeric_cols].corr()
            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0)
            plt.title('Correlation Matrix')
            plt.show()
        
        # 3. Box plots for outlier detection
        if self.numeric_cols:
            n_cols = min(4, len(self.numeric_cols))
            n_rows = (len(self.numeric_cols) + n_cols - 1) // n_cols
            
            plt.figure(figsize=(15, 4 * n_rows))
            for i, col in enumerate(self.numeric_cols):
                plt.subplot(n_rows, n_cols, i + 1)
                plt.boxplot(plot_df_pandas[col].dropna())
                plt.title(f'Box plot of {col}')
                plt.ylabel(col)
            plt.tight_layout()
            plt.show()
    
    def run_full_eda(self, generate_plots: bool = True, plot_sample_size: int = 10000):
        """Run complete EDA pipeline"""
        print("🔍 Starting Comprehensive EDA with Polars")
        print("=" * 60)
        
        # Basic info
        self.basic_info()
        
        # Missing values
        self.missing_values_analysis()
        
        # Numeric summary
        self.numeric_summary()
        
        # Categorical summary
        self.categorical_summary()
        
        # Outlier detection
        self.detect_outliers()
        
        # Correlation analysis
        self.correlation_analysis()
        
        # Data quality check
        self.data_quality_check()
        
        # Generate plots
        if generate_plots:
            self.generate_plots(plot_sample_size)
        
        print("\n" + "=" * 60)
        print("✅ EDA Complete!")

# Run EDA
df = pl.read_csv(
    "__vREP_Customer__202507161242.csv",
    infer_schema_length=10000,
    ignore_errors=True,
    null_values=["", "NULL", "null", "N/A", "n/a", "NA", "P", "#N/A", "#NULL!"]
)

eda = PolarEDA(df)
eda.run_full_eda(generate_plots=False)


# Status options:
#  0 - Ativo
#  1 - Judicial
#  2 - Pré-Judicial
#  3 - Com Plano de Pagamentos
#  4 - Pós-Judicial
#  5 - Inativo
#  6 - Inativo com tanque
#  7 - Pré-Ativo
#  8 - Inativo com Saldo
#  9 - Insolvente
# 10 - PER - Com Plano de Pagamentos
# 11 - Ativo com Baixo Consumo
# 12 - Com Plano de Pagamentos Judicial

# Type options:
#  0 - (Empty)
#  1 - Granel
#  2 - Embalado
#  3 - Canalizado
#  4 - Carvão
#  5 - Supply
#  6 - Outros

df_inactive = df.filter(
    pl.col("Classification").is_in([5,6,8]))

df_active = df.filter(
    ~pl.col("Classification").is_in([5,6,8]))

df_active_empty = df_active.filter(
    pl.col("Type") == 0)
df_active_bulk = df_active.filter(
    pl.col("Type") == 1)
df_active_bottled = df_active.filter(
    pl.col("Type") == 2)
df_active_pipped = df_active.filter(
    pl.col("Type") == 3)
df_active_supply = df_active.filter(
    pl.col("Type") == 5)
df_active_other = df_active.filter(
    pl.col("Type") == 6)

df_inactive_empty = df_inactive.filter(
    pl.col("Type") == 0)
df_inactive_bulk = df_inactive.filter(
    pl.col("Type") == 1)
df_inactive_bottled = df_inactive.filter(
    pl.col("Type") == 2)
df_inactive_pipped = df_inactive.filter(
    pl.col("Type") == 3)
df_inactive_supply = df_inactive.filter(
    pl.col("Type") == 5)
df_inactive_other = df_inactive.filter(
    pl.col("Type") == 6)

# Count Active
print("Active & Empty:", df_active_empty.height)
print("Active & Bulk:", df_active_bulk.height)
print("Active & Bottled:", df_active_bottled.height)
print("Active & Pipped:", df_active_pipped.height)
print("Active & Supply:", df_active_supply.height)
print("Active & Other:", df_active_other.height)
print("=" * 30)
# Count Inactive
print("Inactive & Empty:", df_inactive_empty.height)
print("Inactive & Bulk:", df_inactive_bulk.height)
print("Inactive & Bottled:", df_inactive_bottled.height)
print("Inactive & Pipped:", df_inactive_pipped.height)
print("Inactive & Supply:", df_inactive_supply.height)
print("Inactive & Other:", df_inactive_other.height)

import polars as pl

import json
import polars as pl
from typing import Dict
import os

def load_schema(schema_path: str) -> Dict[str, pl.datatypes.DataType]:
    """
    Load a Frictionless Table Schema (JSON) and convert it into
    a Polars dtypes dictionary suitable for read_csv, read_parquet, etc.
    """
    type_map = {
        "integer": pl.Int64,
        "number": pl.Float64,
        "string": pl.Utf8,
        "boolean": pl.Boolean,
        "date": pl.Date,
        "datetime": pl.Datetime("us"),  # adjust unit if needed
        "year": pl.Int32,
        "time": pl.Time,
        "object": pl.Object,
        # fallback: everything else → Utf8
    }

    if not os.path.exists(schema_path):
        raise FileNotFoundError(f"Schema file not found: {schema_path}")

    if os.path.getsize(schema_path) == 0:
        raise ValueError(f"Schema file is empty: {schema_path}")

    # Try utf-8, fallback to latin1 if it fails
    try:
        with open(schema_path, "r", encoding="utf-8") as f:
            schema = json.load(f)
    except UnicodeDecodeError:
        with open(schema_path, "r", encoding="latin1") as f:
            schema = json.load(f)

    return {
        field["name"]: type_map.get(field["type"], pl.Utf8)
        for field in schema["fields"]
    }

file_name = "__vREP_Customer__202507161242.csv"
schema_path = f"{os.path.basename(file_name).split('.')[0]}.schema.json"

df_vendor = pl.read_csv(file_name, dtypes=load_schema(schema_path)
)

print(df_vendor.height)

df_vendor_test = df_vendor.filter(pl.col("Master Customer No_").cast(pl.Utf8).str.starts_with("C"))

print("Existing Vendors (Excluding Employees):", df_vendor_test.height)

print(df_vendor_test['Master Customer No_'])

# Draw a barplot of the counts of the Classification column