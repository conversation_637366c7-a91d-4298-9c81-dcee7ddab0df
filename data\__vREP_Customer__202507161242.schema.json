{"name": "__vrep_customer__202507161242", "type": "table", "path": "__vREP_Customer__202507161242.csv", "scheme": "file", "format": "csv", "mediatype": "text/csv", "encoding": "utf-8", "schema": {"fields": [{"name": "No_", "type": "String"}, {"name": "Name", "type": "string"}, {"name": "Search Name", "type": "string"}, {"name": "Name 2", "type": "string"}, {"name": "Address", "type": "string"}, {"name": "Address 2", "type": "string"}, {"name": "City", "type": "string"}, {"name": "Contact", "type": "string"}, {"name": "Phone No_", "type": "integer"}, {"name": "Telex No_", "type": "integer"}, {"name": "Document Sending Profile", "type": "string"}, {"name": "Our Account No_", "type": "string"}, {"name": "Territory Code", "type": "string"}, {"name": "Global Dimension 1 Code", "type": "integer"}, {"name": "Global Dimension 2 Code", "type": "integer"}, {"name": "Chain Name", "type": "string"}, {"name": "Budgeted Amount", "type": "number"}, {"name": "Credit Limit (LCY)", "type": "number"}, {"name": "Customer Posting Group", "type": "string"}, {"name": "Currency Code", "type": "string"}, {"name": "Customer Price Group", "type": "string"}, {"name": "Language Code", "type": "string"}, {"name": "Statistics Group", "type": "integer"}, {"name": "Payment Terms Code", "type": "string"}, {"name": "Fin_ Charge Terms Code", "type": "any"}, {"name": "Salesperson Code", "type": "integer"}, {"name": "Shipment Method Code", "type": "integer"}, {"name": "Shipping Agent Code", "type": "string"}, {"name": "Place of Export", "type": "string"}, {"name": "Invoice Disc_ Code", "type": "integer"}, {"name": "Customer Disc_ Group", "type": "string"}, {"name": "Country_Region Code", "type": "string"}, {"name": "Collection Method", "type": "string"}, {"name": "Amount", "type": "number"}, {"name": "Blocked", "type": "integer"}, {"name": "Invoice Copies", "type": "boolean"}, {"name": "Last Statement No_", "type": "integer"}, {"name": "Print Statements", "type": "boolean"}, {"name": "Bill-to Customer No_", "type": "integer"}, {"name": "Priority", "type": "integer"}, {"name": "Payment Method Code", "type": "string"}, {"name": "Last Date Modified", "type": "datetime"}, {"name": "Application Method", "type": "integer"}, {"name": "Prices Including VAT", "type": "boolean"}, {"name": "Location Code", "type": "integer"}, {"name": "Fax No_", "type": "integer"}, {"name": "Telex Answer Back", "type": "string"}, {"name": "VAT Registration No_", "type": "integer"}, {"name": "Combine Shipments", "type": "integer"}, {"name": "Gen_ Bus_ Posting Group", "type": "string"}, {"name": "Picture", "type": "string"}, {"name": "GLN", "type": "string"}, {"name": "Post Code", "type": "string"}, {"name": "County", "type": "string"}, {"name": "E-Mail", "type": "string"}, {"name": "Home Page", "type": "string"}, {"name": "Reminder Terms Code", "type": "string"}, {"name": "No_ Series", "type": "string"}, {"name": "Tax Area Code", "type": "string"}, {"name": "Tax Liable", "type": "integer"}, {"name": "VAT Bus_ Posting Group", "type": "string"}, {"name": "Reserve", "type": "integer"}, {"name": "Block Payment Tolerance", "type": "boolean"}, {"name": "IC Partner Code", "type": "string"}, {"name": "Prepayment _", "type": "number"}, {"name": "Partner Type", "type": "integer"}, {"name": "Image", "type": "string"}, {"name": "Preferred Bank Account Code", "type": "string"}, {"name": "Cash Flow Payment Terms Code", "type": "string"}, {"name": "Primary Contact No_", "type": "string"}, {"name": "Responsibility Center", "type": "string"}, {"name": "Shipping Advice", "type": "boolean"}, {"name": "Shipping Time", "type": "string"}, {"name": "Shipping Agent Service Code", "type": "string"}, {"name": "Service Zone Code", "type": "string"}, {"name": "Allow Line Disc_", "type": "boolean"}, {"name": "Base Calendar Code", "type": "string"}, {"name": "Co<PERSON>-to Addr_ to Qte From", "type": "boolean"}, {"name": "Master Customer No_", "type": "string"}, {"name": "Master", "type": "boolean"}, {"name": "Commission Agent", "type": "string"}, {"name": "CAE Code", "type": "integer"}, {"name": "Mnemonic", "type": "string"}, {"name": "Delivery Remarks", "type": "string"}, {"name": "GPS Latitude", "type": "number"}, {"name": "GPS Longitude", "type": "number"}, {"name": "Generate Orders", "type": "boolean"}, {"name": "Mobile Phone", "type": "integer"}, {"name": "Area Manager", "type": "integer"}, {"name": "Debit Bank", "type": "integer"}, {"name": "Bank Identification No_", "type": "integer"}, {"name": "Debit Authorization Bank No_", "type": "integer"}, {"name": "ADC Maximum Value", "type": "number"}, {"name": "ADC Limit Date", "type": "datetime"}, {"name": "Valid ADC", "type": "integer"}, {"name": "Old ADC", "type": "string"}, {"name": "No_ of Direct Debit Rejections", "type": "integer"}, {"name": "Customer Location Code", "type": "string"}, {"name": "Bin Code", "type": "integer"}, {"name": "SDD Adherence Date", "type": "datetime"}, {"name": "unloadTimeFixed", "type": "integer"}, {"name": "unloadTimeVariablePump", "type": "integer"}, {"name": "unloadTimeVariableGravity", "type": "integer"}, {"name": "unloadTimeVariableHose", "type": "integer"}, {"name": "Created First Orion File", "type": "boolean"}, {"name": "ISP Free", "type": "boolean"}, {"name": "Exemption No_", "type": "string"}, {"name": "Bill-to", "type": "boolean"}, {"name": "Type", "type": "integer"}, {"name": "Invoice", "type": "boolean"}, {"name": "Classification", "type": "integer"}, {"name": "Status", "type": "integer"}, {"name": "<PERSON><PERSON><PERSON> por", "type": "string"}, {"name": "Send Invoice E-Mail", "type": "boolean"}, {"name": "Invoice Combined Day", "type": "integer"}, {"name": "Start Date", "type": "datetime"}, {"name": "End Date", "type": "datetime"}, {"name": "Annual Volume", "type": "number"}, {"name": "Total Volume", "type": "number"}, {"name": "Ship-to", "type": "boolean"}, {"name": "Shortcut Dimension 3", "type": "integer"}, {"name": "Shortcut Dimension 4", "type": "integer"}, {"name": "Shortcut Dimension 5", "type": "integer"}, {"name": "Shortcut Dimension 6", "type": "string"}, {"name": "Shortcut Dimension 7", "type": "string"}, {"name": "Shortcut Dimension 8", "type": "integer"}, {"name": "Send Table Regular Prices", "type": "boolean"}, {"name": "Send Table K Prices", "type": "integer"}, {"name": "E-Mail Precos", "type": "string"}, {"name": "Send Email Daily Deliveries", "type": "integer"}, {"name": "Email Daily Deliveries", "type": "string"}, {"name": "Customer Bulk_Packed CCC No_", "type": "integer"}, {"name": "Customer Bulk_Packed IBAN", "type": "string"}, {"name": "Customer Bulk_Packed SWIFT", "type": "string"}, {"name": "E-Mail GR", "type": "string"}, {"name": "ISP Tax", "type": "integer"}, {"name": "Portal User Name", "type": "string"}, {"name": "IsCustomer", "type": "boolean"}, {"name": "IsDelivery", "type": "boolean"}, {"name": "IsBilling", "type": "boolean"}, {"name": "IsOrderTaker", "type": "boolean"}, {"name": "IsRegistered", "type": "boolean"}, {"name": "LastDateMetacase", "type": "datetime"}, {"name": "<PERSON>er", "type": "string"}, {"name": "Classification GESGAS", "type": "integer"}, {"name": "Not National Taxable Person", "type": "boolean"}, {"name": "Social Security No_", "type": "string"}, {"name": "Registered Cust_ Portal", "type": "integer"}, {"name": "Cell Phone No_", "type": "integer"}, {"name": "Cust_ Portal Password", "type": "string"}, {"name": "Block Payment Agreement", "type": "integer"}, {"name": "Identification Doc_ Type Code", "type": "string"}, {"name": "Identification Doc_ No_", "type": "string"}, {"name": "Preferred Contact Type Code", "type": "string"}, {"name": "ID Doc_ Type Code ES", "type": "string"}, {"name": "ID Doc_ No_ ES", "type": "string"}, {"name": "Customer Type ES", "type": "integer"}, {"name": "Agent No_", "type": "string"}, {"name": "Creation Date", "type": "datetime"}, {"name": "Creation Source", "type": "string"}, {"name": "Utilities Doc_ Copies", "type": "integer"}, {"name": "Non-Paymt_ Periods Code", "type": "string"}, {"name": "Payment Days Code", "type": "string"}, {"name": "Cash VAT Customer", "type": "boolean"}, {"name": "Create Receipt", "type": "boolean"}, {"name": "Default Bank Acc_ Code", "type": "integer"}, {"name": "BP Statistic Code", "type": "string"}, {"name": "BP Debit Pos_ Statistic Code", "type": "string"}, {"name": "BP Credit Pos_ Statistic Code", "type": "string"}, {"name": "Subject to Withholding Tax", "type": "boolean"}, {"name": "Income Type", "type": "string"}, {"name": "Send Email Warnings and NC", "type": "boolean"}, {"name": "<PERSON><PERSON>", "type": "string"}, {"name": "End Consumer", "type": "boolean"}, {"name": "Zone", "type": "boolean"}, {"name": "Generix Web Service Type", "type": "integer"}, {"name": "Completed Car", "type": "boolean"}, {"name": "Completed Container", "type": "boolean"}, {"name": "Containers Qty", "type": "integer"}, {"name": "General E-Mail", "type": "string"}, {"name": "Sales Delegate", "type": "integer"}]}}