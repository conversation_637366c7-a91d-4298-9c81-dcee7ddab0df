import polars as pl
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, dash_table, callback
from dash.dependencies import Input, Output, State
from dash.exceptions import PreventUpdate
import dash_bootstrap_components as dbc
from typing import List, Dict, Any, Optional, Tuple
import warnings
from pathlib import Path
import json
from datetime import datetime, timedelta
warnings.filterwarnings('ignore')

class EnhancedEDADashboard:
    """
    Enhanced EDA Dashboard with comprehensive analysis capabilities
    Supports multiple file formats and provides interactive analysis
    """

    def __init__(self, title: str = "Enhanced EDA Dashboard"):
        self.title = title
        self.df = None
        self.df_sample = None  # For performance with large datasets
        self.numeric_cols = []
        self.categorical_cols = []
        self.datetime_cols = []
        self.boolean_cols = []
        self.text_cols = []

        # Initialize Dash app with Bootstrap theme
        self.app = dash.Dash(
            __name__,
            external_stylesheets=[dbc.themes.BOOTSTRAP, dbc.icons.FONT_AWESOME],
            suppress_callback_exceptions=True
        )

        # Configuration
        self.config = {
            'max_display_rows': 50000,  # Sample size for plots
            'max_categories_display': 20,  # Max categories in categorical plots
            'correlation_threshold': 0.7,  # Highlight strong correlations
            'outlier_threshold': 1.5,  # IQR multiplier for outliers
        }

    def load_data(self, file_path: str, **kwargs) -> bool:
        """
        Load data from various file formats
        """
        file_path = Path(file_path)

        try:
            if file_path.suffix.lower() == '.csv':
                self.df = pl.read_csv(
                    file_path,
                    infer_schema_length=10000,
                    ignore_errors=True,
                    null_values=["", "NULL", "null", "N/A", "n/a", "NA", "#N/A", "#NULL!", "None"],
                    **kwargs
                )
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                # Convert pandas to polars for Excel files
                pd_df = pd.read_excel(file_path, **kwargs)
                self.df = pl.from_pandas(pd_df)
            elif file_path.suffix.lower() == '.parquet':
                self.df = pl.read_parquet(file_path, **kwargs)
            elif file_path.suffix.lower() == '.json':
                self.df = pl.read_json(file_path, **kwargs)
            else:
                raise ValueError(f"Unsupported file format: {file_path.suffix}")

            self._process_data()
            return True

        except Exception as e:
            print(f"Error loading data: {str(e)}")
            return False

    def _process_data(self):
        """Process loaded data and classify columns"""
        if self.df is None:
            return

        # Create sample for performance
        if self.df.height > self.config['max_display_rows']:
            self.df_sample = self.df.sample(self.config['max_display_rows'])
        else:
            self.df_sample = self.df

        # Classify columns
        self._classify_columns()

        print(f"✅ Data loaded successfully: {self.df.shape[0]:,} rows, {self.df.shape[1]} columns")
        print(f"📊 Column types: {len(self.numeric_cols)} numeric, {len(self.categorical_cols)} categorical, "
              f"{len(self.datetime_cols)} datetime, {len(self.boolean_cols)} boolean")

    def _classify_columns(self):
        """Enhanced column classification"""
        self.numeric_cols = []
        self.categorical_cols = []
        self.datetime_cols = []
        self.boolean_cols = []
        self.text_cols = []

        for col in self.df.columns:
            dtype = self.df[col].dtype
            unique_count = self.df[col].n_unique()
            total_count = self.df.height

            # Numeric columns
            if dtype in [pl.Int8, pl.Int16, pl.Int32, pl.Int64,
                        pl.UInt8, pl.UInt16, pl.UInt32, pl.UInt64,
                        pl.Float32, pl.Float64]:
                self.numeric_cols.append(col)

            # Boolean columns
            elif dtype == pl.Boolean or (unique_count == 2 and dtype in [pl.Int8, pl.Int16]):
                self.boolean_cols.append(col)

            # DateTime columns
            elif dtype in [pl.Date, pl.Datetime]:
                self.datetime_cols.append(col)

            # Categorical vs Text columns
            elif dtype in [pl.Utf8, pl.Categorical]:
                # If low cardinality relative to dataset size, treat as categorical
                if unique_count / total_count < 0.5 and unique_count < 100:
                    self.categorical_cols.append(col)
                else:
                    self.text_cols.append(col)

            else:
                # Default to categorical
                self.categorical_cols.append(col)


    def _compute_vertical_spacing(self, n_rows: int, base: float = 0.08) -> float:
        """Return a safe vertical_spacing for Plotly make_subplots.
        Plotly requires vertical_spacing <= 1/(rows-1) when rows > 1, otherwise
        subplots may overlap. This helper computes a spacing that respects that
        limit with a tiny epsilon margin.
        """
        if n_rows <= 1:
            return 0.0
        allowed = 1.0 / (n_rows - 1)
        return max(0.0, min(base, allowed - 1e-6))

    def _create_navbar(self):
        """Create navigation bar"""
        return dbc.Navbar(
            dbc.Container([
                dbc.Row([
                    dbc.Col([
                        html.Img(src="/assets/logo.png", height="30px") if Path("assets/logo.png").exists()
                        else html.I(className="fas fa-chart-line me-2"),
                        dbc.NavbarBrand(self.title, className="ms-2"),
                    ], width="auto"),
                    dbc.Col([
                        dbc.Button(
                            "📁 Load Data",
                            id="load-data-btn",
                            color="primary",
                            size="sm",
                            className="me-2"
                        ),
                        dbc.Button(
                            "💾 Export Report",
                            id="export-btn",
                            color="success",
                            size="sm",
                            disabled=True
                        )
                    ], width="auto")
                ], justify="between")
            ]),
            color="dark",
            dark=True,
            className="mb-4"
        )

    def _create_upload_modal(self):
        """Create file upload modal"""
        return dbc.Modal([
            dbc.ModalHeader("Load Dataset"),
            dbc.ModalBody([
                dcc.Upload(
                    id='upload-data',
                    children=html.Div([
                        html.I(className="fas fa-cloud-upload-alt fa-3x mb-3"),
                        html.Br(),
                        'Drag and Drop or Click to Select Files',
                        html.Br(),
                        html.Small("Supports CSV, Excel, Parquet, JSON", className="text-muted")
                    ]),
                    style={
                        'width': '100%',
                        'height': '200px',
                        'lineHeight': '200px',
                        'borderWidth': '2px',
                        'borderStyle': 'dashed',
                        'borderRadius': '10px',
                        'textAlign': 'center',
                        'margin': '10px',
                        'borderColor': '#007bff'
                    },
                    multiple=False
                ),
                html.Div(id="upload-status", className="mt-3")
            ]),
            dbc.ModalFooter([
                dbc.Button("Close", id="close-upload-modal", className="ms-auto", n_clicks=0)
            ])
        ], id="upload-modal", is_open=False, size="lg")

    def _create_overview_cards(self):
        """Create dataset overview cards"""
        if self.df is None:
            return html.Div("No data loaded", className="text-center text-muted p-5")

        # Calculate statistics
        memory_mb = self.df.estimated_size() / (1024 * 1024)
        missing_cells = sum(self.df[col].null_count() for col in self.df.columns)
        total_cells = self.df.height * self.df.width
        missing_pct = (missing_cells / total_cells) * 100 if total_cells > 0 else 0

        cards_data = [
            {"title": "Rows", "value": f"{self.df.height:,}", "icon": "fas fa-table", "color": "primary"},
            {"title": "Columns", "value": f"{self.df.width}", "icon": "fas fa-columns", "color": "info"},
            {"title": "Memory", "value": f"{memory_mb:.1f} MB", "icon": "fas fa-memory", "color": "warning"},
            {"title": "Missing %", "value": f"{missing_pct:.1f}%", "icon": "fas fa-question-circle", "color": "danger" if missing_pct > 5 else "success"},
            {"title": "Numeric", "value": f"{len(self.numeric_cols)}", "icon": "fas fa-hashtag", "color": "secondary"},
            {"title": "Categorical", "value": f"{len(self.categorical_cols)}", "icon": "fas fa-tags", "color": "secondary"},
        ]

        cards = []
        for card_data in cards_data:
            card = dbc.Card([
                dbc.CardBody([
                    html.Div([
                        html.I(className=f"{card_data['icon']} fa-2x mb-2 text-{card_data['color']}"),
                        html.H3(card_data['value'], className="card-title mb-0"),
                        html.P(card_data['title'], className="card-text text-muted")
                    ], className="text-center")
                ])
            ], className="h-100")
            cards.append(dbc.Col(card, width=2))

        return dbc.Row(cards, className="mb-4")

    def _create_data_quality_section(self):
        """Create comprehensive data quality analysis"""
        if self.df is None:
            return html.Div()

        # Missing values analysis
        missing_data = []
        for col in self.df.columns:
            null_count = self.df[col].null_count()
            if null_count > 0:
                missing_data.append({
                    'Column': col,
                    'Type': str(self.df[col].dtype),
                    'Missing Count': null_count,
                    'Missing %': round((null_count / self.df.height) * 100, 2)
                })

        # Data quality issues
        quality_issues = []

        # Check duplicates
        duplicate_count = self.df.height - self.df.unique().height
        if duplicate_count > 0:
            quality_issues.append({
                'Issue': 'Duplicate Rows',
                'Count': duplicate_count,
                'Severity': 'High' if duplicate_count > self.df.height * 0.1 else 'Medium'
            })

        # Check constant columns
        constant_cols = [col for col in self.df.columns if self.df[col].n_unique() == 1]
        if constant_cols:
            quality_issues.append({
                'Issue': 'Constant Columns',
                'Count': len(constant_cols),
                'Details': ', '.join(constant_cols[:5]) + ('...' if len(constant_cols) > 5 else ''),
                'Severity': 'Medium'
            })

        # High cardinality categorical
        high_card_cols = []
        for col in self.categorical_cols:
            if self.df[col].n_unique() > len(self.categorical_cols) * 10:
                high_card_cols.append(col)

        if high_card_cols:
            quality_issues.append({
                'Issue': 'High Cardinality Categorical',
                'Count': len(high_card_cols),
                'Details': ', '.join(high_card_cols[:3]) + ('...' if len(high_card_cols) > 3 else ''),
                'Severity': 'Low'
            })

        return dbc.Row([
            dbc.Col([
                html.H4("🔍 Data Quality Analysis", className="mb-3"),

                # Missing values chart
                html.H5("Missing Values", className="mt-4 mb-2"),
                dcc.Graph(id="missing-values-chart"),

                # Quality issues table
                html.H5("Quality Issues", className="mt-4 mb-2"),
                dash_table.DataTable(
                    data=quality_issues,
                    columns=[{"name": i, "id": i} for i in (quality_issues[0].keys() if quality_issues else [])],
                    style_cell={'textAlign': 'left', 'padding': '10px'},
                    style_data_conditional=[
                        {
                            'if': {'filter_query': '{Severity} = High'},
                            'backgroundColor': '#ffebee',
                            'color': 'black',
                        },
                        {
                            'if': {'filter_query': '{Severity} = Medium'},
                            'backgroundColor': '#fff3e0',
                            'color': 'black',
                        }
                    ]
                ) if quality_issues else html.P("✅ No quality issues detected!", className="text-success")

            ], width=12)
        ])

    def _create_numeric_analysis_section(self):
        """Create comprehensive numeric analysis"""
        if not self.numeric_cols:
            return html.Div([
                html.H4("📊 Numeric Analysis"),
                html.P("No numeric columns found in the dataset.", className="text-muted")
            ])

        return dbc.Row([
            dbc.Col([
                html.H4("📊 Numeric Analysis", className="mb-3"),

                # Control panel
                dbc.Row([
                    dbc.Col([
                        html.Label("Select Columns:"),
                        dcc.Dropdown(
                            id="numeric-cols-dropdown",
                            options=[{"label": col, "value": col} for col in self.numeric_cols],
                            value=self.numeric_cols[:6],  # Default to first 6
                            multi=True
                        )
                    ], width=6),
                    dbc.Col([
                        html.Label("Plot Type:"),
                        dbc.RadioItems(
                            id="numeric-plot-type",
                            options=[
                                {"label": "Histograms", "value": "hist"},
                                {"label": "Box Plots", "value": "box"},
                                {"label": "Violin Plots", "value": "violin"}
                            ],
                            value="hist",
                            inline=True
                        )
                    ], width=6)
                ], className="mb-3"),

                # Charts
                dcc.Graph(id="numeric-distributions-chart"),

                # Statistics table
                html.H5("Descriptive Statistics", className="mt-4 mb-2"),
                html.Div(id="numeric-stats-table")

            ], width=12)
        ])

    def _create_categorical_analysis_section(self):
        """Create categorical analysis section"""
        if not self.categorical_cols:
            return html.Div([
                html.H4("🏷️ Categorical Analysis"),
                html.P("No categorical columns found in the dataset.", className="text-muted")
            ])

        return dbc.Row([
            dbc.Col([
                html.H4("🏷️ Categorical Analysis", className="mb-3"),

                # Control panel
                dbc.Row([
                    dbc.Col([
                        html.Label("Select Column:"),
                        dcc.Dropdown(
                            id="categorical-col-dropdown",
                            options=[{"label": col, "value": col} for col in self.categorical_cols],
                            value=self.categorical_cols[0] if self.categorical_cols else None
                        )
                    ], width=6),
                    dbc.Col([
                        html.Label("Chart Type:"),
                        dbc.RadioItems(
                            id="categorical-chart-type",
                            options=[
                                {"label": "Bar Chart", "value": "bar"},
                                {"label": "Pie Chart", "value": "pie"},
                                {"label": "Donut Chart", "value": "donut"}
                            ],
                            value="bar",
                            inline=True
                        )
                    ], width=6)
                ], className="mb-3"),

                dcc.Graph(id="categorical-chart")

            ], width=12)
        ])

    def _create_correlation_section(self):
        """Create correlation analysis section"""
        if len(self.numeric_cols) < 2:
            return html.Div([
                html.H4("🔗 Correlation Analysis"),
                html.P("Need at least 2 numeric columns for correlation analysis.", className="text-muted")
            ])

        return dbc.Row([
            dbc.Col([
                html.H4("🔗 Correlation Analysis", className="mb-3"),

                dbc.Row([
                    dbc.Col([
                        html.Label("Correlation Method:"),
                        dcc.Dropdown(
                            id="correlation-method",
                            options=[
                                {"label": "Pearson", "value": "pearson"},
                                {"label": "Spearman", "value": "spearman"}
                            ],
                            value="pearson"
                        )
                    ], width=6),
                    dbc.Col([
                        html.Label("Display:"),
                        dbc.Checklist(
                            id="correlation-options",
                            options=[
                                {"label": "Show values", "value": "show_values"},
                                {"label": "Highlight strong correlations", "value": "highlight"}
                            ],
                            value=["show_values"],
                            inline=True
                        )
                    ], width=6)
                ], className="mb-3"),

                dcc.Graph(id="correlation-heatmap"),

                html.H5("Strong Correlations", className="mt-4 mb-2"),
                html.Div(id="strong-correlations-table")

            ], width=12)
        ])

    def _create_advanced_analysis_section(self):
        """Create advanced analysis section"""
        return dbc.Row([
            dbc.Col([
                html.H4("🧬 Advanced Analysis", className="mb-3"),

                dbc.Tabs([
                    dbc.Tab(label="Outlier Detection", tab_id="outliers"),
                    dbc.Tab(label="Feature Relationships", tab_id="relationships"),
                    dbc.Tab(label="Data Profiling", tab_id="profiling")
                ], id="advanced-tabs", active_tab="outliers"),

                html.Div(id="advanced-content", className="mt-3")

            ], width=12)
        ])

    def create_layout(self):
        """Create the complete dashboard layout"""
        self.app.layout = dbc.Container([
            # Navigation
            self._create_navbar(),

            # Upload modal
            self._create_upload_modal(),

            # Main content
            html.Div(id="main-content", children=[
                # Welcome message when no data
                html.Div([
                    html.Div([
                        html.I(className="fas fa-chart-line fa-5x mb-4 text-primary"),
                        html.H2("Welcome to Enhanced EDA Dashboard", className="mb-3"),
                        html.P("Upload a dataset to begin your exploratory data analysis journey.",
                               className="lead text-muted mb-4"),
                        dbc.Button(
                            "📁 Load Your Data",
                            id="welcome-load-btn",
                            color="primary",
                            size="lg"
                        )
                    ], className="text-center")
                ], id="welcome-section", style={"padding": "100px 0"})
            ]),

            # Content sections (hidden initially)
            html.Div([
                # Overview
                html.Div([
                    html.H3("📋 Dataset Overview", className="mb-4"),
                    self._create_overview_cards()
                ], id="overview-section"),

                html.Hr(),

                # Data Quality
                html.Div(id="quality-section"),

                html.Hr(),

                # Numeric Analysis
                html.Div(id="numeric-section"),

                html.Hr(),

                # Categorical Analysis
                html.Div(id="categorical-section"),

                html.Hr(),

                # Correlation Analysis
                html.Div(id="correlation-section"),

                html.Hr(),

                # Advanced Analysis
                html.Div(id="advanced-section")

            ], id="analysis-sections", style={"display": "none"})

        ], fluid=True)

        self._setup_callbacks()

    def _setup_callbacks(self):
        """Setup all dashboard callbacks"""

        # File upload modal toggle
        @self.app.callback(
            Output("upload-modal", "is_open"),
            [Input("load-data-btn", "n_clicks"),
             Input("welcome-load-btn", "n_clicks"),
             Input("close-upload-modal", "n_clicks")],
            [State("upload-modal", "is_open")]
        )
        def toggle_upload_modal(load_btn, welcome_btn, close_btn, is_open):
            ctx = dash.callback_context
            if not ctx.triggered:
                return False

            trigger_id = ctx.triggered[0]["prop_id"].split(".")[0]

            if trigger_id in ["load-data-btn", "welcome-load-btn"]:
                return True
            elif trigger_id == "close-upload-modal":
                return False

            return is_open

        # Handle file upload and processing
        @self.app.callback(
            [Output("upload-status", "children"),
             Output("main-content", "children"),
             Output("analysis-sections", "style"),
             Output("overview-section", "children"),
             Output("quality-section", "children"),
             Output("numeric-section", "children"),
             Output("categorical-section", "children"),
             Output("correlation-section", "children"),
             Output("advanced-section", "children"),
             Output("export-btn", "disabled")],
            [Input("upload-data", "contents")],
            [State("upload-data", "filename")]
        )
        def handle_file_upload(contents, filename):
            if contents is None:
                return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update

            try:
                # Process the uploaded file
                success = self._process_uploaded_file(contents, filename)

                if success:
                    # Create success message
                    status_msg = dbc.Alert([
                        html.I(className="fas fa-check-circle me-2"),
                        f"Successfully loaded {filename}! Dataset contains {self.df.height:,} rows and {self.df.width} columns."
                    ], color="success", dismissable=True)

                    # Generate all analysis sections
                    overview = [
                        html.H3("📋 Dataset Overview", className="mb-4"),
                        self._create_overview_cards()
                    ]

                    quality_section = self._create_data_quality_section()
                    numeric_section = self._create_numeric_analysis_section()
                    categorical_section = self._create_categorical_analysis_section()
                    correlation_section = self._create_correlation_section()
                    advanced_section = self._create_advanced_analysis_section()

                    # Hide welcome message and show analysis
                    main_content = html.Div()  # Empty div to hide welcome

                    return (status_msg, main_content, {"display": "block"},
                           overview, quality_section, numeric_section,
                           categorical_section, correlation_section,
                           advanced_section, False)

                else:
                    error_msg = dbc.Alert([
                        html.I(className="fas fa-exclamation-triangle me-2"),
                        f"Failed to load {filename}. Please check the file format and try again."
                    ], color="danger")

                    return (error_msg, dash.no_update, dash.no_update,
                           dash.no_update, dash.no_update, dash.no_update,
                           dash.no_update, dash.no_update, dash.no_update, dash.no_update)

            except Exception as e:
                error_msg = dbc.Alert([
                    html.I(className="fas fa-exclamation-triangle me-2"),
                    f"Error processing file: {str(e)}"
                ], color="danger")

                return (error_msg, dash.no_update, dash.no_update,
                       dash.no_update, dash.no_update, dash.no_update,
                       dash.no_update, dash.no_update, dash.no_update, dash.no_update)

        # Missing values chart callback
        @self.app.callback(
            Output("missing-values-chart", "figure"),
            [Input("analysis-sections", "style")],
            prevent_initial_call=True
        )
        def update_missing_values_chart(style):
            if self.df is None or (style and style.get("display") == "none"):
                raise PreventUpdate

            missing_data = []
            for col in self.df.columns:
                null_count = self.df[col].null_count()
                if null_count > 0:
                    missing_data.append({
                        'Column': col,
                        'Missing_Count': null_count,
                        'Missing_Percentage': (null_count / self.df.height) * 100
                    })

            if not missing_data:
                return {}

            df_missing = pd.DataFrame(missing_data)
            fig = px.bar(df_missing,
                        x='Column',
                        y='Missing_Percentage',
                        title="Missing Values by Column (%)",
                        labels={'Missing_Percentage': 'Missing %'},
                        color='Missing_Percentage',
                        color_continuous_scale='Reds')

            fig.update_layout(
                xaxis_tickangle=-45,
                height=400,
                showlegend=False
            )

            return fig

        # Numeric distributions callback
        @self.app.callback(
            [Output("numeric-distributions-chart", "figure"),
             Output("numeric-stats-table", "children")],
            [Input("numeric-cols-dropdown", "value"),
             Input("numeric-plot-type", "value"),
             Input("analysis-sections", "style")],
            prevent_initial_call=True
        )
        def update_numeric_analysis(selected_cols, plot_type, style):
            if (not selected_cols) or (self.df_sample is None) or (style and style.get("display") == "none"):
                raise PreventUpdate

            # Limit to first 6 columns for performance
            cols_to_plot = selected_cols[:6] if len(selected_cols) > 6 else selected_cols

            # Create subplots
            n_cols = len(cols_to_plot)
            n_rows = (n_cols + 1) // 2

            vspace = self._compute_vertical_spacing(n_rows)
            fig = make_subplots(
                rows=n_rows,
                cols=2,
                subplot_titles=cols_to_plot,
                vertical_spacing=vspace
            )

            plot_df = self.df_sample.select(cols_to_plot).to_pandas()

            for i, col in enumerate(cols_to_plot):
                row = (i // 2) + 1
                col_pos = (i % 2) + 1

                if plot_type == "hist":
                    fig.add_trace(
                        go.Histogram(x=plot_df[col].dropna(), name=col, showlegend=False),
                        row=row, col=col_pos
                    )
                elif plot_type == "box":
                    fig.add_trace(
                        go.Box(y=plot_df[col].dropna(), name=col, showlegend=False),
                        row=row, col=col_pos
                    )
                elif plot_type == "violin":
                    fig.add_trace(
                        go.Violin(y=plot_df[col].dropna(), name=col, showlegend=False),
                        row=row, col=col_pos
                    )

            fig.update_layout(height=300 * n_rows, title_text=f"Numeric Column {plot_type.title()}s")

            # Create statistics table
            stats_df = plot_df.describe().round(2)
            stats_table = dash_table.DataTable(
                data=stats_df.reset_index().to_dict('records'),
                columns=[{"name": i, "id": i} for i in stats_df.reset_index().columns],
                style_cell={'textAlign': 'center', 'padding': '10px'},
                style_header={'backgroundColor': '#f8f9fa', 'fontWeight': 'bold'}
            )

            return fig, stats_table

        # Categorical analysis callback
        @self.app.callback(
            Output("categorical-chart", "figure"),
            [Input("categorical-col-dropdown", "value"),
             Input("categorical-chart-type", "value"),
             Input("analysis-sections", "style")],
            prevent_initial_call=True
        )
        def update_categorical_chart(selected_col, chart_type, style):
            if (not selected_col) or (self.df is None) or (style and style.get("display") == "none"):
                raise PreventUpdate

            # Get value counts
            value_counts = (self.df
                          .group_by(selected_col)
                          .agg(pl.count().alias('count'))
                          .sort('count', descending=True)
                          .head(self.config['max_categories_display'])
                          .to_pandas())

            if chart_type == "bar":
                fig = px.bar(value_counts,
                            x=selected_col,
                            y='count',
                            title=f"Distribution of {selected_col}")
                fig.update_layout(xaxis_tickangle=-45)

            elif chart_type == "pie":
                fig = px.pie(value_counts,
                            names=selected_col,
                            values='count',
                            title=f"Distribution of {selected_col}")

            elif chart_type == "donut":
                fig = px.pie(value_counts,
                            names=selected_col,
                            values='count',
                            title=f"Distribution of {selected_col}",
                            hole=0.4)

            fig.update_layout(height=500)
            return fig

        # Correlation heatmap callback
        @self.app.callback(
            [Output("correlation-heatmap", "figure"),
             Output("strong-correlations-table", "children")],
            [Input("correlation-method", "value"),
             Input("correlation-options", "value"),
             Input("analysis-sections", "style")],
            prevent_initial_call=True
        )
        def update_correlation_analysis(method, options, style):
            if (len(self.numeric_cols) < 2) or (self.df_sample is None) or (style and style.get("display") == "none"):
                raise PreventUpdate

            # Calculate correlation matrix
            corr_df = self.df_sample.select(self.numeric_cols).to_pandas()

            if method == "pearson":
                corr_matrix = corr_df.corr()
            else:  # spearman
                corr_matrix = corr_df.corr(method='spearman')

            # Create heatmap
            show_values = "show_values" in (options or [])

            fig = px.imshow(corr_matrix,
                           text_auto=show_values,
                           aspect="auto",
                           title=f"{method.title()} Correlation Matrix",
                           color_continuous_scale='RdBu_r',
                           zmin=-1, zmax=1)

            fig.update_layout(height=600)

            # Find strong correlations
            strong_corr = []
            threshold = self.config['correlation_threshold']

            for i in range(len(corr_matrix.columns)):
                for j in range(i+1, len(corr_matrix.columns)):
                    corr_val = corr_matrix.iloc[i, j]
                    if abs(corr_val) >= threshold:
                        strong_corr.append({
                            'Variable 1': corr_matrix.columns[i],
                            'Variable 2': corr_matrix.columns[j],
                            'Correlation': round(corr_val, 3),
                            'Strength': 'Strong' if abs(corr_val) >= 0.8 else 'Moderate'
                        })

            if strong_corr:
                corr_table = dash_table.DataTable(
                    data=strong_corr,
                    columns=[{"name": i, "id": i} for i in strong_corr[0].keys()],
                    style_cell={'textAlign': 'center', 'padding': '10px'},
                    style_data_conditional=[
                        {
                            'if': {'filter_query': '{Strength} = Strong'},
                            'backgroundColor': '#ffebee',
                            'color': 'black',
                        }
                    ]
                )
            else:
                corr_table = html.P("No strong correlations found.", className="text-muted")

            return fig, corr_table

    def _process_uploaded_file(self, contents, filename):
        """Process uploaded file content"""
        import base64
        import io

        try:
            # Parse the data URL
            content_type, content_string = contents.split(',')
            decoded = base64.b64decode(content_string)

            print(f"Processing file: {filename}")  # Debug log

            if filename.endswith('.csv'):
                # Read CSV with more robust handling
                try:
                    # First, read with pandas for more lenient type inference
                    df_pd = pd.read_csv(
                        io.StringIO(decoded.decode('utf-8')),
                        low_memory=False,  # Read entire file to infer types properly
                        na_values=["", "NULL", "null", "N/A", "n/a", "NA", "#N/A", "#NULL!", "None", "NaN"],
                        keep_default_na=True,
                        dtype=str  # Read everything as string first to avoid conversion errors
                    )

                    # Convert to polars with safe type conversion
                    self.df = pl.from_pandas(df_pd)

                    # Try to infer and convert types safely in polars
                    self._safe_type_conversion()

                except UnicodeDecodeError:
                    # Try different encodings
                    for encoding in ['latin1', 'iso-8859-1', 'cp1252']:
                        try:
                            df_pd = pd.read_csv(
                                io.StringIO(decoded.decode(encoding)),
                                low_memory=False,
                                na_values=["", "NULL", "null", "N/A", "n/a", "NA", "#N/A", "#NULL!", "None", "NaN"],
                                keep_default_na=True,
                                dtype=str
                            )
                            self.df = pl.from_pandas(df_pd)
                            self._safe_type_conversion()
                            break
                        except UnicodeDecodeError:
                            continue
                    else:
                        raise ValueError("Could not decode the file with common encodings")

                except Exception as e:
                    print(f"Pandas failed: {e}, trying polars with safe settings")
                    # Fallback: Use polars with very conservative settings
                    import tempfile
                    import os
                    with tempfile.NamedTemporaryFile(mode='wb', suffix='.csv', delete=False) as tmp_file:
                        tmp_file.write(decoded)
                        tmp_file_path = tmp_file.name

                    try:
                        self.df = pl.read_csv(
                            tmp_file_path,
                            infer_schema_length=0,  # Don't infer schema, read as strings
                            ignore_errors=True,
                            null_values=["", "NULL", "null", "N/A", "n/a", "NA", "#N/A", "#NULL!", "None", "NaN"],
                            dtypes={col: pl.Utf8 for col in range(100)}  # Force string type for first 100 columns
                        )
                        self._safe_type_conversion()
                    finally:
                        os.unlink(tmp_file_path)

            elif filename.endswith(('.xlsx', '.xls')):
                # Read Excel
                df_pd = pd.read_excel(
                    io.BytesIO(decoded),
                    dtype=str,  # Read as strings first
                    na_values=["", "NULL", "null", "N/A", "n/a", "NA", "#N/A", "#NULL!", "None", "NaN"]
                )
                self.df = pl.from_pandas(df_pd)
                self._safe_type_conversion()

            elif filename.endswith('.json'):
                # Read JSON
                df_pd = pd.read_json(io.StringIO(decoded.decode('utf-8')))
                self.df = pl.from_pandas(df_pd)
                self._safe_type_conversion()

            else:
                print(f"Unsupported file format: {filename}")
                return False

            print(f"Successfully loaded: {self.df.shape[0]} rows, {self.df.shape[1]} columns")
            self._process_data()
            return True

        except Exception as e:
            print(f"Error processing file {filename}: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _safe_type_conversion(self):
        """Safely convert column types after loading as strings"""
        if self.df is None:
            return

        # Try to convert each column to appropriate types
        conversions = []

        for col in self.df.columns:
            try:
                # Get non-null sample of the column
                sample = self.df[col].drop_nulls().head(1000)

                if sample.len() == 0:
                    # All nulls, keep as string
                    continue
            except Exception as e:
                print(f"Error sampling column {col}: {e}")
                continue

            # Try integer conversion first
            try:
                # Remove common formatting characters and try conversion
                cleaned_sample = sample.str.replace_all(r'[,$\s]', '').str.replace_all(r'\.0+$', '')
            except Exception as e:
                print(f"Error cleaning sample for column {col}: {e}")
                continue

    def _create_main_content(self):
        """Create main content after data is loaded"""
        return [
            self._create_data_quality_section(),
            self._create_numeric_analysis_section(),
            self._create_categorical_analysis_section(),
            self._create_correlation_section(),
            self._create_advanced_analysis_section()
        ]

    def run_dashboard(self, debug=True, host='127.0.0.1', port=8050):
        """Run the dashboard server"""
        self.create_layout()
        print(f"🚀 Starting Enhanced EDA Dashboard at http://{host}:{port}")
        print(f"📊 Ready to analyze your datasets!")
        self.app.run(debug=debug, host=host, port=port)

# Example usage and data loading helper
def load_sample_data():
    """Create sample data for testing"""
    np.random.seed(42)
    n_samples = 1000

    data = {
        'age': np.random.normal(35, 12, n_samples).astype(int),
        'income': np.random.exponential(50000, n_samples),
        'education': np.random.choice(['High School', 'Bachelor', 'Master', 'PhD'], n_samples, p=[0.3, 0.4, 0.2, 0.1]),
        'city': np.random.choice(['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'], n_samples),
        'satisfaction': np.random.randint(1, 11, n_samples),
        'date_joined': pd.date_range('2020-01-01', periods=n_samples, freq='D')[:n_samples],
        'is_active': np.random.choice([True, False], n_samples, p=[0.7, 0.3])
    }

    # Add some missing values
    mask = np.random.random(n_samples) < 0.05
    data['income'][mask] = None

    return pl.from_pandas(pd.DataFrame(data))

if __name__ == "__main__":
    # Initialize dashboard
    dashboard = EnhancedEDADashboard(title="My EDA Dashboard")

    # For quick local testing, load sample data and process it; comment out to load real files instead
    dashboard.df = load_sample_data()
    dashboard._process_data()

    # Run the dashboard
    dashboard.run_dashboard(debug=True, port=8050)