"""Utilities for loading, profiling and caching data for the Dash EDA app.

We intentionally keep heavy data server-side, passing only small JSON payloads
(sampled frames and metadata) to the browser via dcc.Store to avoid memory bloat.
"""
from __future__ import annotations

import base64
import io
import json
import hashlib
from dataclasses import dataclass
from functools import lru_cache
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import polars as pl

from .config import LIMITS


@dataclass
class DataBundle:
    df: pl.DataFrame
    df_sample: pl.DataFrame
    numeric: List[str]
    categorical: List[str]
    datetime: List[str]
    boolean: List[str]
    text: List[str]
    dataset_id: str  # hash-based identifier for caching


# ------------------------------ Loading ------------------------------------ #

def _safe_from_pandas(df_pd: pd.DataFrame) -> pl.DataFrame:
    try:
        return pl.from_pandas(df_pd)
    except Exception:
        # Coerce to strings as a fallback
        for c in df_pd.columns:
            df_pd[c] = df_pd[c].astype("string")
        return pl.from_pandas(df_pd)


def load_from_upload(contents: str, filename: str) -> pl.DataFrame:
    """Decode a dcc.Upload payload and return a Polars DataFrame.
    This function is robust to common encoding problems.
    """
    content_type, content_string = contents.split(",")
    decoded = base64.b64decode(content_string)

    if filename.lower().endswith(".csv"):
        try:
            df_pd = pd.read_csv(io.StringIO(decoded.decode("utf-8")), low_memory=False)
        except UnicodeDecodeError:
            for enc in ("latin1", "iso-8859-1", "cp1252"):
                try:
                    df_pd = pd.read_csv(io.StringIO(decoded.decode(enc)), low_memory=False)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise
        return _safe_from_pandas(df_pd)

    if filename.lower().endswith((".xlsx", ".xls")):
        df_pd = pd.read_excel(io.BytesIO(decoded))
        return _safe_from_pandas(df_pd)

    if filename.lower().endswith(".json"):
        df_pd = pd.read_json(io.StringIO(decoded.decode("utf-8")))
        return _safe_from_pandas(df_pd)

    raise ValueError(f"Unsupported file format: {filename}")


# --------------------------- Processing / Meta ------------------------------ #

def classify_columns(df: pl.DataFrame) -> Tuple[List[str], List[str], List[str], List[str], List[str]]:
    numeric: List[str] = []
    categorical: List[str] = []
    datetime: List[str] = []
    boolean: List[str] = []
    text: List[str] = []

    for col in df.columns:
        dtype = df[col].dtype
        uniq = df[col].n_unique()
        if dtype in [pl.Int8, pl.Int16, pl.Int32, pl.Int64, pl.UInt8, pl.UInt16, pl.UInt32, pl.UInt64, pl.Float32, pl.Float64]:
            numeric.append(col)
        elif dtype == pl.Boolean:
            boolean.append(col)
        elif dtype in [pl.Date, pl.Datetime]:
            datetime.append(col)
        elif dtype in [pl.Utf8, pl.Categorical]:
            if uniq < 100 and uniq / max(1, df.height) < 0.5:
                categorical.append(col)
            else:
                text.append(col)
        else:
            categorical.append(col)

    return numeric, categorical, datetime, boolean, text


def sample_df(df: pl.DataFrame, max_rows: int = LIMITS.max_display_rows) -> pl.DataFrame:
    if df.height <= max_rows:
        return df
    # Use deterministic sample (head) for reproducibility and caching stability
    return df.head(max_rows)


def make_bundle(df: pl.DataFrame) -> DataBundle:
    df_sample = sample_df(df)
    numeric, categorical, dt, boolean, text = classify_columns(df)

    # Build a content-hash based on schema and the first 1,000 rows of the sample
    h = hashlib.sha256()
    h.update("|".join(df.columns).encode())
    h.update("|".join(map(lambda x: str(df[x].dtype), df.columns)).encode())
    head_json = df_sample.head(1000).to_pandas().to_json(orient="split")
    h.update(head_json.encode())
    dataset_id = h.hexdigest()[:16]

    return DataBundle(
        df=df,
        df_sample=df_sample,
        numeric=numeric,
        categorical=categorical,
        datetime=dt,
        boolean=boolean,
        text=text,
        dataset_id=dataset_id,
    )


def bundle_to_stores(bundle: DataBundle) -> Tuple[dict, dict]:
    """Return (data_store, meta_store) JSON-serializable payloads.
    The data store keeps only the sampled frame to bound browser memory usage.
    """
    data_store = {
        "dataset_id": bundle.dataset_id,
        "df_sample": bundle.df_sample.to_pandas().to_json(orient="split", date_format="iso"),
    }
    meta_store = {
        "rows": int(bundle.df.shape[0]),
        "cols": int(bundle.df.shape[1]),
        "numeric": bundle.numeric,
        "categorical": bundle.categorical,
        "datetime": bundle.datetime,
        "boolean": bundle.boolean,
        "text": bundle.text,
        "memory_mb": float(bundle.df.estimated_size() / (1024 * 1024)),
    }
    return data_store, meta_store


def stores_to_pandas(data_store: dict) -> pd.DataFrame:
    if not data_store or "df_sample" not in data_store:
        raise ValueError("Empty data store")
    # Wrap JSON string in StringIO per pandas deprecation guidance
    return pd.read_json(io.StringIO(data_store["df_sample"]), orient="split")


# ----------------------------- Cached compute ------------------------------- #

@lru_cache(maxsize=16)
def cached_corr_json(dataset_id: str, method: str, cols_key: str) -> str:
    """Return a JSON (split) correlation matrix for a dataset.
    We cache by dataset_id + method + cols_key to avoid recomputing.
    """
    # This function is designed to be called via the thin wrapper below that
    # provides the actual data. Using lru_cache here avoids storing large
    # pandas frames in dcc.Store.
    raise RuntimeError("Use compute_correlation wrapper with data frame")


def compute_correlation(df_pd: pd.DataFrame, dataset_id: str, method: str, cols: List[str]) -> pd.DataFrame:
    if len(cols) < 2:
        return pd.DataFrame()
    cols_key = "|".join(cols)
    key = (dataset_id, method, cols_key)

    # Try cache first (manually emulate since cached function cannot accept df)
    try:
        # access lru cache internal dict (best effort):
        for k, v in cached_corr_json.cache_info()._asdict().items():  # type: ignore[attr-defined]
            break
    except Exception:
        pass

    # Lightweight manual cache using function attribute
    if not hasattr(compute_correlation, "_cache"):
        compute_correlation._cache = {}  # type: ignore[attr-defined]
    cache: Dict[str, str] = compute_correlation._cache  # type: ignore[attr-defined]
    ckey = f"{dataset_id}:{method}:{cols_key}"
    if ckey in cache:
        # Wrap JSON string in StringIO per pandas deprecation guidance
        return pd.read_json(io.StringIO(cache[ckey]), orient="split")

    corr = df_pd[cols].corr(method=method)
    cache[ckey] = corr.to_json(orient="split")
    # Trim cache size
    if len(cache) > 16:
        for _ in range(len(cache) - 16):
            cache.pop(next(iter(cache)))
    return corr


# ------------------------------ Stats helpers ------------------------------- #

def descriptive_stats(df_pd: pd.DataFrame, cols: List[str]) -> pd.DataFrame:
    if not cols:
        return pd.DataFrame()
    return df_pd[cols].describe().round(2).reset_index()


def value_counts(df: pl.DataFrame, col: str, top: int = LIMITS.max_categories_display) -> pd.DataFrame:
    if col not in df.columns:
        return pd.DataFrame(columns=[col, "count"])
    vc = (
        df.group_by(col)
        .agg(pl.count().alias("count"))
        .sort("count", descending=True)
        .head(top)
        .to_pandas()
    )
    return vc


def outliers_iqr(series: pd.Series) -> pd.DataFrame:
    q1 = series.quantile(0.25)
    q3 = series.quantile(0.75)
    iqr = q3 - q1
    lower = q1 - 1.5 * iqr
    upper = q3 + 1.5 * iqr
    out_mask = (series < lower) | (series > upper)
    return pd.DataFrame({"value": series[out_mask]})


def strong_correlations(corr: pd.DataFrame, threshold: float) -> pd.DataFrame:
    rows: List[Tuple[str, str, float]] = []
    cols = list(corr.columns)
    for i in range(len(cols)):
        for j in range(i + 1, len(cols)):
            v = float(corr.iloc[i, j])
            if abs(v) >= threshold:
                rows.append((cols[i], cols[j], round(v, 3)))
    return pd.DataFrame(rows, columns=["Variable 1", "Variable 2", "Correlation"])

