{"cells": [{"cell_type": "code", "execution_count": 8, "id": "f38dbb9a", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import List, Dict, Any, Optional\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "class PolarEDA:\n", "    \"\"\"\n", "    Comprehensive EDA class for large datasets using Polars\n", "    Optimized for datasets with dimensions like 200x50,000\n", "    \"\"\"\n", "    \n", "    def __init__(self, df: pl.DataFrame):\n", "        self.df = df\n", "        self.numeric_cols = []\n", "        self.categorical_cols = []\n", "        self.datetime_cols = []\n", "        self._classify_columns()\n", "        \n", "    def _classify_columns(self):\n", "        \"\"\"Classify columns by data type\"\"\"\n", "        for col in self.df.columns:\n", "            dtype = self.df[col].dtype\n", "            \n", "            if dtype in [pl.Int8, pl.Int16, pl.Int32, pl.Int64, pl.UInt8, pl.UInt16, pl.UInt32, pl.UInt64, pl.Float32, pl.Float64]:\n", "                self.numeric_cols.append(col)\n", "            elif dtype in [pl.Utf8, pl.Categorical]:\n", "                self.categorical_cols.append(col)\n", "            elif dtype in [pl.Date, pl.Datetime]:\n", "                self.datetime_cols.append(col)\n", "    \n", "    def basic_info(self) -> Dict[str, Any]:\n", "        \"\"\"Get basic dataset information\"\"\"\n", "        info = {\n", "            'shape': self.df.shape,\n", "            'columns': len(self.df.columns),\n", "            'memory_usage_mb': self.df.estimated_size() / (1024 * 1024),\n", "            'numeric_columns': len(self.numeric_cols),\n", "            'categorical_columns': len(self.categorical_cols),\n", "            'datetime_columns': len(self.datetime_cols)\n", "        }\n", "        \n", "        print(\"=\" * 50)\n", "        print(\"DATASET OVERVIEW\")\n", "        print(\"=\" * 50)\n", "        for key, value in info.items():\n", "            if key == 'memory_usage_mb':\n", "                print(f\"{key.replace('_', ' ').title()}: {value:.2f} MB\")\n", "            else:\n", "                print(f\"{key.replace('_', ' ').title()}: {value}\")\n", "        \n", "        return info\n", "    \n", "    def missing_values_analysis(self) -> pl.DataFrame:\n", "        \"\"\"Analyze missing values efficiently\"\"\"\n", "        print(\"\\n\" + \"=\" * 50)\n", "        print(\"MISSING VALUES ANALYSIS\")\n", "        print(\"=\" * 50)\n", "        \n", "        # Use per-column aliasing for compatibility with Polars\n", "        missing_stats = self.df.select([\n", "            *(pl.col(col).null_count().alias(f\"{col}_null_count\") for col in self.df.columns),\n", "            *(pl.col(col).count().alias(f\"{col}_total_count\") for col in self.df.columns)\n", "        ])\n", "        \n", "        # Calculate missing percentages\n", "        missing_df = pl.DataFrame({\n", "            'column': self.df.columns,\n", "            'missing_count': [missing_stats[f\"{col}_null_count\"][0] for col in self.df.columns],\n", "            'total_count': [missing_stats[f\"{col}_total_count\"][0] for col in self.df.columns]\n", "        }).with_columns([\n", "            (pl.col('missing_count') / pl.col('total_count') * 100).alias('missing_percentage')\n", "        ]).filter(pl.col('missing_count') > 0).sort('missing_percentage', descending=True)\n", "        \n", "        if missing_df.height > 0:\n", "            print(missing_df)\n", "        else:\n", "            print(\"No missing values found!\")\n", "        \n", "        return missing_df\n", "    \n", "    def numeric_summary(self) -> pl.DataFrame:\n", "        \"\"\"Generate summary statistics for numeric columns\"\"\"\n", "        if not self.numeric_cols:\n", "            print(\"No numeric columns found!\")\n", "            return pl.<PERSON><PERSON><PERSON>e()\n", "        \n", "        print(\"\\n\" + \"=\" * 50)\n", "        print(\"NUMERIC COLUMNS SUMMARY\")\n", "        print(\"=\" * 50)\n", "        \n", "        # Use Polars' efficient describe method\n", "        summary = self.df.select(self.numeric_cols).describe()\n", "        print(summary)\n", "        \n", "        return summary\n", "    \n", "    def categorical_summary(self) -> Dict[str, pl.DataFrame]:\n", "        \"\"\"Analyze categorical columns\"\"\"\n", "        if not self.categorical_cols:\n", "            print(\"No categorical columns found!\")\n", "            return {}\n", "        \n", "        print(\"\\n\" + \"=\" * 50)\n", "        print(\"CATEGORICAL COLUMNS SUMMARY\")\n", "        print(\"=\" * 50)\n", "        \n", "        cat_summaries = {}\n", "        for col in self.categorical_cols:\n", "            print(f\"\\nColumn: {col}\")\n", "            \n", "            # Value counts (top 10 to avoid overwhelming output)\n", "            value_counts = (self.df\n", "                          .group_by(col)\n", "                          .agg(pl.count().alias('count'))\n", "                          .sort('count', descending=True)\n", "                          .head(10))\n", "            \n", "            unique_count = self.df[col].n_unique()\n", "            \n", "            print(f\"Unique values: {unique_count}\")\n", "            print(\"Top 10 most frequent values:\")\n", "            print(value_counts)\n", "            \n", "            cat_summaries[col] = value_counts\n", "        \n", "        return cat_summaries\n", "    \n", "    def detect_outliers(self, method: str = 'iqr') -> Dict[str, pl.DataFrame]:\n", "        \"\"\"Detect outliers in numeric columns\"\"\"\n", "        if not self.numeric_cols:\n", "            return {}\n", "        \n", "        print(\"\\n\" + \"=\" * 50)\n", "        print(f\"OUTLIER DETECTION ({method.upper()} METHOD)\")\n", "        print(\"=\" * 50)\n", "        \n", "        outlier_info = {}\n", "        \n", "        for col in self.numeric_cols:\n", "            if method == 'iqr':\n", "                # Calculate quartiles\n", "                q1 = self.df[col].quantile(0.25)\n", "                q3 = self.df[col].quantile(0.75)\n", "                iqr = q3 - q1\n", "                \n", "                lower_bound = q1 - 1.5 * iqr\n", "                upper_bound = q3 + 1.5 * iqr\n", "                \n", "                outliers = self.df.filter(\n", "                    (pl.col(col) < lower_bound) | (pl.col(col) > upper_bound)\n", "                ).select([col])\n", "                \n", "                outlier_count = outliers.height\n", "                outlier_percentage = (outlier_count / self.df.height) * 100\n", "                \n", "                print(f\"{col}: {outlier_count} outliers ({outlier_percentage:.2f}%)\")\n", "                outlier_info[col] = outliers\n", "        \n", "        return outlier_info\n", "    \n", "    def correlation_analysis(self) -> pl.DataFrame:\n", "        \"\"\"Calculate correlation matrix for numeric columns\"\"\"\n", "        if len(self.numeric_cols) < 2:\n", "            print(\"Need at least 2 numeric columns for correlation analysis!\")\n", "            return pl.<PERSON><PERSON><PERSON>e()\n", "        print(\"\\n\" + \"=\" * 50)\n", "        print(\"CORRELATION ANALYSIS\")\n", "        print(\"=\" * 50)\n", "        \n", "        # Convert to numpy for correlation calculation (Polars doesn't have native corr yet)\n", "        numeric_data = self.df.select(self.numeric_cols).to_numpy()\n", "        corr_matrix = np.corrcoef(numeric_data.T)\n", "        \n", "        # Create correlation DataFrame\n", "        corr_df = pl.DataFrame(\n", "            corr_matrix,\n", "            schema=self.numeric_cols\n", "        ).with_columns(\n", "            pl.Series(\"column\", self.numeric_cols)\n", "        ).select([\"column\"] + self.numeric_cols)\n", "        \n", "        print(corr_df)\n", "        \n", "        return corr_df\n", "    \n", "    def data_quality_check(self) -> Dict[str, Any]:\n", "        \"\"\"Comprehensive data quality assessment\"\"\"\n", "        print(\"\\n\" + \"=\" * 50)\n", "        print(\"DATA QUALITY CHECK\")\n", "        print(\"=\" * 50)\n", "        \n", "        quality_report = {}\n", "        \n", "        # Check for duplicates\n", "        duplicate_count = self.df.height - self.df.unique().height\n", "        quality_report['duplicates'] = duplicate_count\n", "        \n", "        # Check for constant columns\n", "        constant_cols = []\n", "        for col in self.df.columns:\n", "            if self.df[col].n_unique() == 1:\n", "                constant_cols.append(col)\n", "        quality_report['constant_columns'] = constant_cols\n", "        \n", "        # Check for high cardinality categorical columns\n", "        high_cardinality_cols = []\n", "        for col in self.categorical_cols:\n", "            unique_ratio = self.df[col].n_unique() / self.df.height\n", "            if unique_ratio > 0.5:\n", "                high_cardinality_cols.append(col)\n", "        quality_report['high_cardinality_categorical'] = high_cardinality_cols\n", "        \n", "        print(f\"Duplicate rows: {duplicate_count}\")\n", "        print(f\"Constant columns: {constant_cols}\")\n", "        print(f\"High cardinality categorical columns: {high_cardinality_cols}\")\n", "        \n", "        return quality_report\n", "    \n", "    def generate_plots(self, sample_size: int = 10000):\n", "        \"\"\"Generate key visualizations (with sampling for large datasets)\"\"\"\n", "        print(\"\\n\" + \"=\" * 50)\n", "        print(\"GENERATING VISUALIZATIONS\")\n", "        print(\"=\" * 50)\n", "        \n", "        # Sample data for plotting if dataset is large\n", "        if self.df.height > sample_size:\n", "            plot_df = self.df.sample(sample_size)\n", "            print(f\"Using sample of {sample_size} rows for plotting\")\n", "        else:\n", "            plot_df = self.df\n", "        \n", "        # Convert to pandas for plotting (matplotlib/seaborn compatibility)\n", "        plot_df_pandas = plot_df.to_pandas()\n", "        \n", "        # 1. Distribution plots for numeric columns\n", "        if self.numeric_cols:\n", "            n_cols = min(4, len(self.numeric_cols))\n", "            n_rows = (len(self.numeric_cols) + n_cols - 1) // n_cols\n", "            \n", "            plt.figure(figsize=(15, 4 * n_rows))\n", "            for i, col in enumerate(self.numeric_cols):\n", "                plt.subplot(n_rows, n_cols, i + 1)\n", "                plt.hist(plot_df_pandas[col].dropna(), bins=50, alpha=0.7)\n", "                plt.title(f'Distribution of {col}')\n", "                plt.xlabel(col)\n", "                plt.ylabel('Frequency')\n", "            plt.tight_layout()\n", "            plt.show()\n", "        \n", "        # 2. Correlation heatmap\n", "        if len(self.numeric_cols) > 1:\n", "            plt.figure(figsize=(12, 8))\n", "            corr_matrix = plot_df_pandas[self.numeric_cols].corr()\n", "            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0)\n", "            plt.title('Correlation Matrix')\n", "            plt.show()\n", "        \n", "        # 3. Box plots for outlier detection\n", "        if self.numeric_cols:\n", "            n_cols = min(4, len(self.numeric_cols))\n", "            n_rows = (len(self.numeric_cols) + n_cols - 1) // n_cols\n", "            \n", "            plt.figure(figsize=(15, 4 * n_rows))\n", "            for i, col in enumerate(self.numeric_cols):\n", "                plt.subplot(n_rows, n_cols, i + 1)\n", "                plt.boxplot(plot_df_pandas[col].dropna())\n", "                plt.title(f'Box plot of {col}')\n", "                plt.ylabel(col)\n", "            plt.tight_layout()\n", "            plt.show()\n", "    \n", "    def run_full_eda(self, generate_plots: bool = True, plot_sample_size: int = 10000):\n", "        \"\"\"Run complete EDA pipeline\"\"\"\n", "        print(\"🔍 Starting Comprehensive EDA with Polars\")\n", "        print(\"=\" * 60)\n", "        \n", "        # Basic info\n", "        self.basic_info()\n", "        \n", "        # Missing values\n", "        self.missing_values_analysis()\n", "        \n", "        # Numeric summary\n", "        self.numeric_summary()\n", "        \n", "        # Categorical summary\n", "        self.categorical_summary()\n", "        \n", "        # Outlier detection\n", "        self.detect_outliers()\n", "        \n", "        # Correlation analysis\n", "        self.correlation_analysis()\n", "        \n", "        # Data quality check\n", "        self.data_quality_check()\n", "        \n", "        # Generate plots\n", "        if generate_plots:\n", "            self.generate_plots(plot_sample_size)\n", "        \n", "        print(\"\\n\" + \"=\" * 60)\n", "        print(\"✅ EDA Complete!\")"]}, {"cell_type": "code", "execution_count": 9, "id": "f034e2d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Starting Comprehensive EDA with Polars\n", "============================================================\n", "==================================================\n", "DATASET OVERVIEW\n", "==================================================\n", "Shape: (48800, 181)\n", "Columns: 181\n", "Memory Usage Mb: 50.55 MB\n", "Numeric Columns: 90\n", "Categorical Columns: 91\n", "Datetime Columns: 0\n", "\n", "==================================================\n", "MISSING VALUES ANALYSIS\n", "==================================================\n", "shape: (107, 4)\n", "┌────────────────────────┬───────────────┬─────────────┬────────────────────┐\n", "│ column                 ┆ missing_count ┆ total_count ┆ missing_percentage │\n", "│ ---                    ┆ ---           ┆ ---         ┆ ---                │\n", "│ str                    ┆ i64           ┆ i64         ┆ f64                │\n", "╞════════════════════════╪═══════════════╪═════════════╪════════════════════╡\n", "│ Our Account No_        ┆ 48800         ┆ 0           ┆ inf                │\n", "│ Territory Code         ┆ 48800         ┆ 0           ┆ inf                │\n", "│ Chain Name             ┆ 48800         ┆ 0           ┆ inf                │\n", "│ Fin_ Charge Terms Code ┆ 48800         ┆ 0           ┆ inf                │\n", "│ Shipping Agent Code    ┆ 48800         ┆ 0           ┆ inf                │\n", "│ …                      ┆ …             ┆ …           ┆ …                  │\n", "│ Post Code              ┆ 119           ┆ 48681       ┆ 0.244449           │\n", "│ City                   ┆ 96            ┆ 48704       ┆ 0.197109           │\n", "│ Country_Region Code    ┆ 36            ┆ 48764       ┆ 0.073825           │\n", "│ Address                ┆ 33            ┆ 48767       ┆ 0.067669           │\n", "│ Name                   ┆ 16            ┆ 48784       ┆ 0.032798           │\n", "└────────────────────────┴───────────────┴─────────────┴────────────────────┘\n", "\n", "==================================================\n", "NUMERIC COLUMNS SUMMARY\n", "==================================================\n", "shape: (9, 91)\n", "┌───────────┬───────────┬───────────┬───────────┬───┬───────────┬───────────┬───────────┬──────────┐\n", "│ statistic ┆ No_       ┆ Telex No_ ┆ Global    ┆ … ┆ Completed ┆ Completed ┆ Container ┆ Sales    │\n", "│ ---       ┆ ---       ┆ ---       ┆ Dimension ┆   ┆ Car       ┆ Container ┆ s Qty     ┆ Delegate │\n", "│ str       ┆ f64       ┆ f64       ┆ 1 Code    ┆   ┆ ---       ┆ ---       ┆ ---       ┆ ---      │\n", "│           ┆           ┆           ┆ ---       ┆   ┆ f64       ┆ f64       ┆ f64       ┆ f64      │\n", "│           ┆           ┆           ┆ f64       ┆   ┆           ┆           ┆           ┆          │\n", "╞═══════════╪═══════════╪═══════════╪═══════════╪═══╪═══════════╪═══════════╪═══════════╪══════════╡\n", "│ count     ┆ 17027.0   ┆ 1.0       ┆ 47964.0   ┆ … ┆ 48800.0   ┆ 48800.0   ┆ 48800.0   ┆ 409.0    │\n", "│ null_coun ┆ 31773.0   ┆ 48799.0   ┆ 836.0     ┆ … ┆ 0.0       ┆ 0.0       ┆ 0.0       ┆ 48391.0  │\n", "│ t         ┆           ┆           ┆           ┆   ┆           ┆           ┆           ┆          │\n", "│ mean      ┆ 5.0944e7  ┆ 123.0     ┆ 145624.12 ┆ … ┆ 0.003074  ┆ 0.004672  ┆ 0.007398  ┆ 5.0095e7 │\n", "│           ┆           ┆           ┆ 1987      ┆   ┆           ┆           ┆           ┆          │\n", "│ std       ┆ 1.9537e7  ┆ null      ┆ 19210.587 ┆ … ┆ 0.055357  ┆ 0.068194  ┆ 0.123337  ┆ 3.141186 │\n", "│           ┆           ┆           ┆ 642       ┆   ┆           ┆           ┆           ┆          │\n", "│ min       ┆ 41147.0   ┆ 123.0     ┆ 100000.0  ┆ … ┆ 0.0       ┆ 0.0       ┆ 0.0       ┆ 5.009449 │\n", "│           ┆           ┆           ┆           ┆   ┆           ┆           ┆           ┆ 8e7      │\n", "│ 25%       ┆ 5.001091e ┆ 123.0     ┆ 120006.0  ┆ … ┆ 0.0       ┆ 0.0       ┆ 0.0       ┆ 5.00945e │\n", "│           ┆ 7         ┆           ┆           ┆   ┆           ┆           ┆           ┆ 7        │\n", "│ 50%       ┆ 5.0030509 ┆ 123.0     ┆ 160000.0  ┆ … ┆ 0.0       ┆ 0.0       ┆ 0.0       ┆ 5.009450 │\n", "│           ┆ e7        ┆           ┆           ┆   ┆           ┆           ┆           ┆ 2e7      │\n", "│ 75%       ┆ 7.0002751 ┆ 123.0     ┆ 160000.0  ┆ … ┆ 0.0       ┆ 0.0       ┆ 0.0       ┆ 5.009450 │\n", "│           ┆ e7        ┆           ┆           ┆   ┆           ┆           ┆           ┆ 5e7      │\n", "│ max       ┆ 7.0007325 ┆ 123.0     ┆ 340000.0  ┆ … ┆ 1.0       ┆ 1.0       ┆ 4.0       ┆ 5.009450 │\n", "│           ┆ e7        ┆           ┆           ┆   ┆           ┆           ┆           ┆ 9e7      │\n", "└───────────┴───────────┴───────────┴───────────┴───┴───────────┴───────────┴───────────┴──────────┘\n", "\n", "==================================================\n", "CATEGORICAL COLUMNS SUMMARY\n", "==================================================\n", "\n", "Column: Name\n", "Unique values: 42691\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Name                            ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ CERRO LAMI, SOC TURISTICA SA    ┆ 38    │\n", "│ null                            ┆ 16    │\n", "│ SIMOSIL - COMERCIO INDUSTRIA E… ┆ 12    │\n", "│ Vila Galé Soc Emp Turisticos S… ┆ 11    │\n", "│ ROCHINVEST, SA                  ┆ 10    │\n", "│ MYSTIC ISLANDS, UNIPESSOAL LDA  ┆ 10    │\n", "│ AGOSTINHO JOAQUIM CALDEIRA      ┆ 9     │\n", "│ Aviludo- <PERSON>d. <PERSON><PERSON><PERSON>… ┆ 7     │\n", "│ Amorim <PERSON>             ┆ 7     │\n", "│ JOAQUIM ALVES DOS SANTOS        ┆ 7     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: Search Name\n", "Unique values: 38764\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Search Name                     ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 3986  │\n", "│ CERRO LAMI, SOC TURISTICA SA    ┆ 38    │\n", "│ VILA GALÉ SOC EMP TURISTICOS S… ┆ 10    │\n", "│ ROCHINVEST, SA                  ┆ 10    │\n", "│ AGOSTINHO JOAQUIM CALDEIRA      ┆ 9     │\n", "│ JOAQUIM ALVES DOS SANTOS        ┆ 7     │\n", "│ AVILUDO- IND. COMERCIO PRODUTO… ┆ 7     │\n", "│ AMORIM FLORESTAL SA             ┆ 7     │\n", "│ JOAQUIM BAPTISTA DA COSTA       ┆ 7     │\n", "│ ADRIAN PETER WRIGHT             ┆ 6     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: Name 2\n", "Unique values: 1422\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────┬───────┐\n", "│ Name 2          ┆ count │\n", "│ ---             ┆ ---   │\n", "│ str             ┆ u32   │\n", "╞═════════════════╪═══════╡\n", "│ null            ┆ 46584 │\n", "│ LDA             ┆ 117   │\n", "│ S.A.            ┆ 40    │\n", "│ Lda             ┆ 38    │\n", "│ Lda.            ┆ 25    │\n", "│ SA              ┆ 19    │\n", "│ UNIPESSOAL, LDA ┆ 17    │\n", "│ Unipessoal Lda  ┆ 16    │\n", "│  CIVIL LDA      ┆ 12    │\n", "│                 ┆ 11    │\n", "└─────────────────┴───────┘\n", "\n", "Column: Address\n", "Unique values: 42439\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Address                         ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ <PERSON>chal                         ┆ 90    │\n", "│ null                            ┆ 33    │\n", "│ AV. Alto do Fogo                ┆ 28    │\n", "│ <PERSON><PERSON> <PERSON>        ┆ 25    │\n", "│ R. DA BELA VISTA                ┆ 24    │\n", "│ <PERSON><PERSON> <PERSON>  ┆ 22    │\n", "│ R. DR. BERNARDO PEREIRA LEITE   ┆ 22    │\n", "│ EDF. VILA MEÃ, R<PERSON> <PERSON>. <PERSON> … ┆ 21    │\n", "│ R. Central de Ribaçais          ┆ 20    │\n", "│ Rua 25 de Abril                 ┆ 18    │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: Address 2\n", "Unique values: 1568\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────────────┬───────┐\n", "│ Address 2                ┆ count │\n", "│ ---                      ┆ ---   │\n", "│ str                      ┆ u32   │\n", "╞══════════════════════════╪═══════╡\n", "│ null                     ┆ 46831 │\n", "│ Q                        ┆ 26    │\n", "│ O                        ┆ 22    │\n", "│ ENT                      ┆ 16    │\n", "│ SQ                       ┆ 15    │\n", "│ ASC                      ┆ 13    │\n", "│ ESQ                      ┆ 12    │\n", "│ TO                       ┆ 11    │\n", "│ DTO                      ┆ 9     │\n", "│ Edf. BelaVista Residence ┆ 8     │\n", "└──────────────────────────┴───────┘\n", "\n", "Column: City\n", "Unique values: 3027\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────────┬───────┐\n", "│ City          ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ TAVIRA        ┆ 2069  │\n", "│ PONTA DELGADA ┆ 1238  │\n", "│ LAGOS         ┆ 886   │\n", "│ ALBUFEIRA     ┆ 859   │\n", "│ LOURINHÃ      ┆ 835   │\n", "│ FARO          ┆ 741   │\n", "│ PORTIMÃO      ┆ 699   │\n", "│ SINES         ┆ 691   │\n", "│ LISBOA        ┆ 604   │\n", "│ ALMANCIL      ┆ 471   │\n", "└───────────────┴───────┘\n", "\n", "Column: Contact\n", "Unique values: 1117\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Contact                         ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 47191 │\n", "│ Eng <PERSON>                  ┆ 25    │\n", "│ Engº <PERSON><PERSON>                 ┆ 20    │\n", "│ Gerência                        ┆ 12    │\n", "│ Eng<PERSON> <PERSON><PERSON>                 ┆ 8     │\n", "│ <PERSON>                   ┆ 6     │\n", "│ <PERSON><PERSON>                    ┆ 6     │\n", "│ <PERSON><PERSON>                       ┆ 5     │\n", "│ <PERSON>                      ┆ 5     │\n", "│ Sr <PERSON> /<PERSON><PERSON> / DªMaria … ┆ 5     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: Phone No_\n", "Unique values: 38764\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Search Name                     ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 3986  │\n", "│ CERRO LAMI, SOC TURISTICA SA    ┆ 38    │\n", "│ VILA GALÉ SOC EMP TURISTICOS S… ┆ 10    │\n", "│ ROCHINVEST, SA                  ┆ 10    │\n", "│ AGOSTINHO JOAQUIM CALDEIRA      ┆ 9     │\n", "│ JOAQUIM ALVES DOS SANTOS        ┆ 7     │\n", "│ AVILUDO- IND. COMERCIO PRODUTO… ┆ 7     │\n", "│ AMORIM FLORESTAL SA             ┆ 7     │\n", "│ JOAQUIM BAPTISTA DA COSTA       ┆ 7     │\n", "│ ADRIAN PETER WRIGHT             ┆ 6     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: Name 2\n", "Unique values: 1422\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────┬───────┐\n", "│ Name 2          ┆ count │\n", "│ ---             ┆ ---   │\n", "│ str             ┆ u32   │\n", "╞═════════════════╪═══════╡\n", "│ null            ┆ 46584 │\n", "│ LDA             ┆ 117   │\n", "│ S.A.            ┆ 40    │\n", "│ Lda             ┆ 38    │\n", "│ Lda.            ┆ 25    │\n", "│ SA              ┆ 19    │\n", "│ UNIPESSOAL, LDA ┆ 17    │\n", "│ Unipessoal Lda  ┆ 16    │\n", "│  CIVIL LDA      ┆ 12    │\n", "│                 ┆ 11    │\n", "└─────────────────┴───────┘\n", "\n", "Column: Address\n", "Unique values: 42439\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Address                         ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ <PERSON>chal                         ┆ 90    │\n", "│ null                            ┆ 33    │\n", "│ AV. Alto do Fogo                ┆ 28    │\n", "│ <PERSON><PERSON> <PERSON>        ┆ 25    │\n", "│ R. DA BELA VISTA                ┆ 24    │\n", "│ <PERSON><PERSON> <PERSON>  ┆ 22    │\n", "│ R. DR. BERNARDO PEREIRA LEITE   ┆ 22    │\n", "│ EDF. VILA MEÃ, R<PERSON> <PERSON>. <PERSON> … ┆ 21    │\n", "│ R. Central de Ribaçais          ┆ 20    │\n", "│ Rua 25 de Abril                 ┆ 18    │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: Address 2\n", "Unique values: 1568\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────────────┬───────┐\n", "│ Address 2                ┆ count │\n", "│ ---                      ┆ ---   │\n", "│ str                      ┆ u32   │\n", "╞══════════════════════════╪═══════╡\n", "│ null                     ┆ 46831 │\n", "│ Q                        ┆ 26    │\n", "│ O                        ┆ 22    │\n", "│ ENT                      ┆ 16    │\n", "│ SQ                       ┆ 15    │\n", "│ ASC                      ┆ 13    │\n", "│ ESQ                      ┆ 12    │\n", "│ TO                       ┆ 11    │\n", "│ DTO                      ┆ 9     │\n", "│ Edf. BelaVista Residence ┆ 8     │\n", "└──────────────────────────┴───────┘\n", "\n", "Column: City\n", "Unique values: 3027\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────────┬───────┐\n", "│ City          ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ TAVIRA        ┆ 2069  │\n", "│ PONTA DELGADA ┆ 1238  │\n", "│ LAGOS         ┆ 886   │\n", "│ ALBUFEIRA     ┆ 859   │\n", "│ LOURINHÃ      ┆ 835   │\n", "│ FARO          ┆ 741   │\n", "│ PORTIMÃO      ┆ 699   │\n", "│ SINES         ┆ 691   │\n", "│ LISBOA        ┆ 604   │\n", "│ ALMANCIL      ┆ 471   │\n", "└───────────────┴───────┘\n", "\n", "Column: Contact\n", "Unique values: 1117\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Contact                         ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 47191 │\n", "│ Eng <PERSON>                  ┆ 25    │\n", "│ Engº <PERSON><PERSON>                 ┆ 20    │\n", "│ Gerência                        ┆ 12    │\n", "│ Eng<PERSON> <PERSON><PERSON>                 ┆ 8     │\n", "│ <PERSON>                   ┆ 6     │\n", "│ <PERSON><PERSON>                    ┆ 6     │\n", "│ <PERSON><PERSON>                       ┆ 5     │\n", "│ <PERSON>                      ┆ 5     │\n", "│ Sr <PERSON> /<PERSON><PERSON> / DªMaria … ┆ 5     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: Phone No_\n", "Unique values: 14674\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────┬───────┐\n", "│ Phone No_ ┆ count │\n", "│ ---       ┆ ---   │\n", "│ str       ┆ u32   │\n", "╞═══════════╪═══════╡\n", "│ null      ┆ 27030 │\n", "│ 218425300 ┆ 300   │\n", "│ 291223438 ┆ 121   │\n", "│ 259348630 ┆ 79    │\n", "│ 291922223 ┆ 77    │\n", "│ 281325635 ┆ 74    │\n", "│ 282690300 ┆ 61    │\n", "│ 226162269 ┆ 39    │\n", "│ 232940775 ┆ 35    │\n", "│ 912223569 ┆ 33    │\n", "└───────────┴───────┘\n", "\n", "Column: Document Sending Profile\n", "Unique values: 2\n", "Top 10 most frequent values:\n", "shape: (2, 2)\n", "┌──────────────────────────┬───────┐\n", "│ Document Sending Profile ┆ count │\n", "│ ---                      ┆ ---   │\n", "│ str                      ┆ u32   │\n", "╞══════════════════════════╪═══════╡\n", "│ null                     ┆ 43562 │\n", "│ 001                      ┆ 5238  │\n", "└──────────────────────────┴───────┘\n", "\n", "Column: Our Account No_\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────┬───────┐\n", "│ Our Account No_ ┆ count │\n", "│ ---             ┆ ---   │\n", "│ str             ┆ u32   │\n", "╞═════════════════╪═══════╡\n", "│ null            ┆ 48800 │\n", "└─────────────────┴───────┘\n", "\n", "Column: Territory Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────────┬───────┐\n", "│ Territory Code ┆ count │\n", "│ ---            ┆ ---   │\n", "│ str            ┆ u32   │\n", "╞════════════════╪═══════╡\n", "│ null           ┆ 48800 │\n", "└────────────────┴───────┘\n", "\n", "Column: Chain Name\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────┬───────┐\n", "│ Chain Name ┆ count │\n", "│ ---        ┆ ---   │\n", "│ str        ┆ u32   │\n", "╞════════════╪═══════╡\n", "│ null       ┆ 48800 │\n", "└────────────┴───────┘\n", "\n", "Column: Customer Posting Group\n", "Unique values: 7\n", "Top 10 most frequent values:\n", "shape: (7, 2)\n", "┌────────────────────────┬───────┐\n", "│ Customer Posting Group ┆ count │\n", "│ ---                    ┆ ---   │\n", "│ str                    ┆ u32   │\n", "╞════════════════════════╪═══════╡\n", "│ NAC                    ┆ 47891 │\n", "│ null                   ┆ 892   │\n", "│ OUT                    ┆ 7     │\n", "│ CDNAC                  ┆ 3     │\n", "│ EMP                    ┆ 3     │\n", "│ UE                     ┆ 3     │\n", "│ NACFR                  ┆ 1     │\n", "└────────────────────────┴───────┘\n", "\n", "Column: Currency Code\n", "Unique values: 2\n", "Top 10 most frequent values:\n", "shape: (2, 2)\n", "┌───────────────┬───────┐\n", "│ Currency Code ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 48797 │\n", "│ USD           ┆ 3     │\n", "└───────────────┴───────┘\n", "\n", "Column: Customer Price Group\n", "Unique values: 56\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────────┬───────┐\n", "│ Customer Price Group ┆ count │\n", "│ ---                  ┆ ---   │\n", "│ str                  ┆ u32   │\n", "╞══════════════════════╪═══════╡\n", "│ null                 ┆ 40904 │\n", "│ GW F+                ┆ 1523  │\n", "│ GS XA                ┆ 1403  │\n", "│ GS XE                ┆ 714   │\n", "│ GS XD                ┆ 707   │\n", "│ GS XF                ┆ 705   │\n", "│ GS XB                ┆ 492   │\n", "│ GS XG                ┆ 487   │\n", "│ GS XC                ┆ 369   │\n", "│ GS XI                ┆ 252   │\n", "└──────────────────────┴───────┘\n", "\n", "Column: Language Code\n", "Unique values: 14674\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────┬───────┐\n", "│ Phone No_ ┆ count │\n", "│ ---       ┆ ---   │\n", "│ str       ┆ u32   │\n", "╞═══════════╪═══════╡\n", "│ null      ┆ 27030 │\n", "│ 218425300 ┆ 300   │\n", "│ 291223438 ┆ 121   │\n", "│ 259348630 ┆ 79    │\n", "│ 291922223 ┆ 77    │\n", "│ 281325635 ┆ 74    │\n", "│ 282690300 ┆ 61    │\n", "│ 226162269 ┆ 39    │\n", "│ 232940775 ┆ 35    │\n", "│ 912223569 ┆ 33    │\n", "└───────────┴───────┘\n", "\n", "Column: Document Sending Profile\n", "Unique values: 2\n", "Top 10 most frequent values:\n", "shape: (2, 2)\n", "┌──────────────────────────┬───────┐\n", "│ Document Sending Profile ┆ count │\n", "│ ---                      ┆ ---   │\n", "│ str                      ┆ u32   │\n", "╞══════════════════════════╪═══════╡\n", "│ null                     ┆ 43562 │\n", "│ 001                      ┆ 5238  │\n", "└──────────────────────────┴───────┘\n", "\n", "Column: Our Account No_\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────┬───────┐\n", "│ Our Account No_ ┆ count │\n", "│ ---             ┆ ---   │\n", "│ str             ┆ u32   │\n", "╞═════════════════╪═══════╡\n", "│ null            ┆ 48800 │\n", "└─────────────────┴───────┘\n", "\n", "Column: Territory Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────────┬───────┐\n", "│ Territory Code ┆ count │\n", "│ ---            ┆ ---   │\n", "│ str            ┆ u32   │\n", "╞════════════════╪═══════╡\n", "│ null           ┆ 48800 │\n", "└────────────────┴───────┘\n", "\n", "Column: Chain Name\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────┬───────┐\n", "│ Chain Name ┆ count │\n", "│ ---        ┆ ---   │\n", "│ str        ┆ u32   │\n", "╞════════════╪═══════╡\n", "│ null       ┆ 48800 │\n", "└────────────┴───────┘\n", "\n", "Column: Customer Posting Group\n", "Unique values: 7\n", "Top 10 most frequent values:\n", "shape: (7, 2)\n", "┌────────────────────────┬───────┐\n", "│ Customer Posting Group ┆ count │\n", "│ ---                    ┆ ---   │\n", "│ str                    ┆ u32   │\n", "╞════════════════════════╪═══════╡\n", "│ NAC                    ┆ 47891 │\n", "│ null                   ┆ 892   │\n", "│ OUT                    ┆ 7     │\n", "│ CDNAC                  ┆ 3     │\n", "│ EMP                    ┆ 3     │\n", "│ UE                     ┆ 3     │\n", "│ NACFR                  ┆ 1     │\n", "└────────────────────────┴───────┘\n", "\n", "Column: Currency Code\n", "Unique values: 2\n", "Top 10 most frequent values:\n", "shape: (2, 2)\n", "┌───────────────┬───────┐\n", "│ Currency Code ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 48797 │\n", "│ USD           ┆ 3     │\n", "└───────────────┴───────┘\n", "\n", "Column: Customer Price Group\n", "Unique values: 56\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────────┬───────┐\n", "│ Customer Price Group ┆ count │\n", "│ ---                  ┆ ---   │\n", "│ str                  ┆ u32   │\n", "╞══════════════════════╪═══════╡\n", "│ null                 ┆ 40904 │\n", "│ GW F+                ┆ 1523  │\n", "│ GS XA                ┆ 1403  │\n", "│ GS XE                ┆ 714   │\n", "│ GS XD                ┆ 707   │\n", "│ GS XF                ┆ 705   │\n", "│ GS XB                ┆ 492   │\n", "│ GS XG                ┆ 487   │\n", "│ GS XC                ┆ 369   │\n", "│ GS XI                ┆ 252   │\n", "└──────────────────────┴───────┘\n", "\n", "Column: Language Code\n", "Unique values: 5\n", "Top 10 most frequent values:\n", "shape: (5, 2)\n", "┌───────────────┬───────┐\n", "│ Language Code ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 46380 │\n", "│ E             ┆ 2409  │\n", "│ S             ┆ 7     │\n", "│ D             ┆ 3     │\n", "│ N             ┆ 1     │\n", "└───────────────┴───────┘\n", "\n", "Column: Payment Terms Code\n", "Unique values: 19\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────────────┬───────┐\n", "│ Payment Terms Code ┆ count │\n", "│ ---                ┆ ---   │\n", "│ str                ┆ u32   │\n", "╞════════════════════╪═══════╡\n", "│ C093               ┆ 33095 │\n", "│ C408               ┆ 6247  │\n", "│ null               ┆ 3780  │\n", "│ C406               ┆ 3453  │\n", "│ C133               ┆ 616   │\n", "│ C409               ┆ 401   │\n", "│ C158               ┆ 316   │\n", "│ UTIL               ┆ 282   │\n", "│ C402               ┆ 217   │\n", "│ C401               ┆ 148   │\n", "└────────────────────┴───────┘\n", "\n", "Column: Fin_ Charge Terms Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────────────────┬───────┐\n", "│ Fin_ Charge Terms Code ┆ count │\n", "│ ---                    ┆ ---   │\n", "│ str                    ┆ u32   │\n", "╞════════════════════════╪═══════╡\n", "│ null                   ┆ 48800 │\n", "└────────────────────────┴───────┘\n", "\n", "Column: Shipping Agent Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────┬───────┐\n", "│ Shipping Agent Code ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ null                ┆ 48800 │\n", "└─────────────────────┴───────┘\n", "\n", "Column: Place of Export\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────┬───────┐\n", "│ Place of Export ┆ count │\n", "│ ---             ┆ ---   │\n", "│ str             ┆ u32   │\n", "╞═════════════════╪═══════╡\n", "│ null            ┆ 48800 │\n", "└─────────────────┴───────┘\n", "\n", "Column: Customer Disc_ Group\n", "Unique values: 3\n", "Top 10 most frequent values:\n", "shape: (3, 2)\n", "┌──────────────────────┬───────┐\n", "│ Customer Disc_ Group ┆ count │\n", "│ ---                  ┆ ---   │\n", "│ str                  ┆ u32   │\n", "╞══════════════════════╪═══════╡\n", "│ null                 ┆ 48770 │\n", "│ ZD02                 ┆ 20    │\n", "│ 5                    ┆ 10    │\n", "└──────────────────────┴───────┘\n", "\n", "Column: Country_Region Code\n", "Unique values: 29\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────┬───────┐\n", "│ Country_Region Code ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ PT                  ┆ 48509 │\n", "│ GB                  ┆ 50    │\n", "│ US                  ┆ 40    │\n", "│ null                ┆ 36    │\n", "│ FR                  ┆ 30    │\n", "│ GI                  ┆ 27    │\n", "│ DE                  ┆ 22    │\n", "│ NL                  ┆ 16    │\n", "│ CH                  ┆ 11    │\n", "│ BE                  ┆ 11    │\n", "└─────────────────────┴───────┘\n", "\n", "Column: Collection Method\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────────┬───────┐\n", "│ Collection Method ┆ count │\n", "│ ---               ┆ ---   │\n", "│ str               ┆ u32   │\n", "╞═══════════════════╪═══════╡\n", "│ null              ┆ 48800 │\n", "└───────────────────┴───────┘\n", "\n", "Column: Payment Method Code\n", "Unique values: 5\n", "Top 10 most frequent values:\n", "shape: (5, 2)\n", "┌─────────────────────┬───────┐\n", "│ Payment Method Code ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ M                   ┆ 21678 │\n", "│ D                   ┆ 13170 │\n", "│ C                   ┆ 6418  │\n", "│ T                   ┆ 3810  │\n", "│ null                ┆ 3724  │\n", "└─────────────────────┴───────┘\n", "\n", "Column: Last Date Modified\n", "Unique values: 1992\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Last Date Modified      ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 2019-01-19 00:00:00.000 ┆ 8153  │\n", "│ 2021-03-15 00:00:00.000 ┆ 4698  │\n", "│ 2022-03-10 00:00:00.000 ┆ 1248  │\n", "│ 2018-03-16 00:00:00.000 ┆ 720   │\n", "│ 2021-11-22 00:00:00.000 ┆ 516   │\n", "│ 2020-08-27 00:00:00.000 ┆ 437   │\n", "│ 2022-06-22 00:00:00.000 ┆ 388   │\n", "│ 2019-02-12 00:00:00.000 ┆ 383   │\n", "│ 2019-06-01 00:00:00.000 ┆ 358   │\n", "│ 2024-01-12 00:00:00.000 ┆ 333   │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Fax No_\n", "Unique values: 5\n", "Top 10 most frequent values:\n", "shape: (5, 2)\n", "┌───────────────┬───────┐\n", "│ Language Code ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 46380 │\n", "│ E             ┆ 2409  │\n", "│ S             ┆ 7     │\n", "│ D             ┆ 3     │\n", "│ N             ┆ 1     │\n", "└───────────────┴───────┘\n", "\n", "Column: Payment Terms Code\n", "Unique values: 19\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────────────┬───────┐\n", "│ Payment Terms Code ┆ count │\n", "│ ---                ┆ ---   │\n", "│ str                ┆ u32   │\n", "╞════════════════════╪═══════╡\n", "│ C093               ┆ 33095 │\n", "│ C408               ┆ 6247  │\n", "│ null               ┆ 3780  │\n", "│ C406               ┆ 3453  │\n", "│ C133               ┆ 616   │\n", "│ C409               ┆ 401   │\n", "│ C158               ┆ 316   │\n", "│ UTIL               ┆ 282   │\n", "│ C402               ┆ 217   │\n", "│ C401               ┆ 148   │\n", "└────────────────────┴───────┘\n", "\n", "Column: Fin_ Charge Terms Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────────────────┬───────┐\n", "│ Fin_ Charge Terms Code ┆ count │\n", "│ ---                    ┆ ---   │\n", "│ str                    ┆ u32   │\n", "╞════════════════════════╪═══════╡\n", "│ null                   ┆ 48800 │\n", "└────────────────────────┴───────┘\n", "\n", "Column: Shipping Agent Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────┬───────┐\n", "│ Shipping Agent Code ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ null                ┆ 48800 │\n", "└─────────────────────┴───────┘\n", "\n", "Column: Place of Export\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────┬───────┐\n", "│ Place of Export ┆ count │\n", "│ ---             ┆ ---   │\n", "│ str             ┆ u32   │\n", "╞═════════════════╪═══════╡\n", "│ null            ┆ 48800 │\n", "└─────────────────┴───────┘\n", "\n", "Column: Customer Disc_ Group\n", "Unique values: 3\n", "Top 10 most frequent values:\n", "shape: (3, 2)\n", "┌──────────────────────┬───────┐\n", "│ Customer Disc_ Group ┆ count │\n", "│ ---                  ┆ ---   │\n", "│ str                  ┆ u32   │\n", "╞══════════════════════╪═══════╡\n", "│ null                 ┆ 48770 │\n", "│ ZD02                 ┆ 20    │\n", "│ 5                    ┆ 10    │\n", "└──────────────────────┴───────┘\n", "\n", "Column: Country_Region Code\n", "Unique values: 29\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────┬───────┐\n", "│ Country_Region Code ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ PT                  ┆ 48509 │\n", "│ GB                  ┆ 50    │\n", "│ US                  ┆ 40    │\n", "│ null                ┆ 36    │\n", "│ FR                  ┆ 30    │\n", "│ GI                  ┆ 27    │\n", "│ DE                  ┆ 22    │\n", "│ NL                  ┆ 16    │\n", "│ CH                  ┆ 11    │\n", "│ BE                  ┆ 11    │\n", "└─────────────────────┴───────┘\n", "\n", "Column: Collection Method\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────────┬───────┐\n", "│ Collection Method ┆ count │\n", "│ ---               ┆ ---   │\n", "│ str               ┆ u32   │\n", "╞═══════════════════╪═══════╡\n", "│ null              ┆ 48800 │\n", "└───────────────────┴───────┘\n", "\n", "Column: Payment Method Code\n", "Unique values: 5\n", "Top 10 most frequent values:\n", "shape: (5, 2)\n", "┌─────────────────────┬───────┐\n", "│ Payment Method Code ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ M                   ┆ 21678 │\n", "│ D                   ┆ 13170 │\n", "│ C                   ┆ 6418  │\n", "│ T                   ┆ 3810  │\n", "│ null                ┆ 3724  │\n", "└─────────────────────┴───────┘\n", "\n", "Column: Last Date Modified\n", "Unique values: 1992\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Last Date Modified      ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 2019-01-19 00:00:00.000 ┆ 8153  │\n", "│ 2021-03-15 00:00:00.000 ┆ 4698  │\n", "│ 2022-03-10 00:00:00.000 ┆ 1248  │\n", "│ 2018-03-16 00:00:00.000 ┆ 720   │\n", "│ 2021-11-22 00:00:00.000 ┆ 516   │\n", "│ 2020-08-27 00:00:00.000 ┆ 437   │\n", "│ 2022-06-22 00:00:00.000 ┆ 388   │\n", "│ 2019-02-12 00:00:00.000 ┆ 383   │\n", "│ 2019-06-01 00:00:00.000 ┆ 358   │\n", "│ 2024-01-12 00:00:00.000 ┆ 333   │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Fax No_\n", "Unique values: 2213\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────┬───────┐\n", "│ Fax No_   ┆ count │\n", "│ ---       ┆ ---   │\n", "│ str       ┆ u32   │\n", "╞═══════════╪═══════╡\n", "│ null      ┆ 44868 │\n", "│ 291923395 ┆ 74    │\n", "│ 220932544 ┆ 25    │\n", "│ 241372982 ┆ 11    │\n", "│ 259348631 ┆ 11    │\n", "│ 296205359 ┆ 9     │\n", "│ 282342928 ┆ 9     │\n", "│ 275771331 ┆ 8     │\n", "│ 289398309 ┆ 7     │\n", "│ 282341969 ┆ 6     │\n", "└───────────┴───────┘\n", "\n", "Column: Telex Answer Back\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────────┬───────┐\n", "│ Telex Answer Back ┆ count │\n", "│ ---               ┆ ---   │\n", "│ str               ┆ u32   │\n", "╞═══════════════════╪═══════╡\n", "│ null              ┆ 48800 │\n", "└───────────────────┴───────┘\n", "\n", "Column: Gen_ Bus_ Posting Group\n", "Unique values: 6\n", "Top 10 most frequent values:\n", "shape: (6, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Gen_ Bus_ Posting Group ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ NAC                     ┆ 44820 │\n", "│ ACORES                  ┆ 2183  │\n", "│ null                    ┆ 1151  │\n", "│ MADEIRA                 ┆ 603   │\n", "│ OUT                     ┆ 25    │\n", "│ UE                      ┆ 18    │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Picture\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────┬───────┐\n", "│ Picture ┆ count │\n", "│ ---     ┆ ---   │\n", "│ str     ┆ u32   │\n", "╞═════════╪═══════╡\n", "│ null    ┆ 48800 │\n", "└─────────┴───────┘\n", "\n", "Column: GLN\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌──────┬───────┐\n", "│ GLN  ┆ count │\n", "│ ---  ┆ ---   │\n", "│ str  ┆ u32   │\n", "╞══════╪═══════╡\n", "│ null ┆ 48800 │\n", "└──────┴───────┘\n", "\n", "Column: Post Code\n", "Unique values: 11603\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────┬───────┐\n", "│ Post Code ┆ count │\n", "│ ---       ┆ ---   │\n", "│ str       ┆ u32   │\n", "╞═══════════╪═══════╡\n", "│ 8800-216  ┆ 389   │\n", "│ 9500-374  ┆ 377   │\n", "│ 7520-313  ┆ 346   │\n", "│ 8600-781  ┆ 252   │\n", "│ 0000-000  ┆ 241   │\n", "│ 8800-608  ┆ 198   │\n", "│ 8400-141  ┆ 192   │\n", "│ 4950-855  ┆ 180   │\n", "│ 8800-600  ┆ 178   │\n", "│ 2135-042  ┆ 171   │\n", "└───────────┴───────┘\n", "\n", "Column: County\n", "Unique values: 170\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────────────┬───────┐\n", "│ County             ┆ count │\n", "│ ---                ┆ ---   │\n", "│ str                ┆ u32   │\n", "╞════════════════════╪═══════╡\n", "│ Faro               ┆ 8899  │\n", "│ Porto              ┆ 6027  │\n", "│ Lisboa             ┆ 3800  │\n", "│ Leiria             ┆ 3670  │\n", "│ Braga              ┆ 2772  │\n", "│ Aveiro             ┆ 2750  │\n", "│ Viana do Castelo   ┆ 2455  │\n", "│ FARO               ┆ 1931  │\n", "│ <PERSON><PERSON>bal            ┆ 1766  │\n", "│ Ilha de São Miguel ┆ 1743  │\n", "└────────────────────┴───────┘\n", "\n", "Column: E-Mail\n", "Unique values: 8\n", "Top 10 most frequent values:\n", "shape: (8, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ E-Mail                          ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 48791 │\n", "│ s.vasco<PERSON><PERSON>@rubisenergia.pt   ┆ 3     │\n", "│ <EMAIL>    ┆ 1     │\n", "│ <EMAIL>     ┆ 1     │\n", "│ <EMAIL>       ┆ 1     │\n", "│ <EMAIL>          ┆ 1     │\n", "│ casadas<PERSON><PERSON>@gmail.com         ┆ 1     │\n", "│ filipe.rod<PERSON>ues-adh@mosquetei… ┆ 1     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: <PERSON> Page\n", "Unique values: 127\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Home Page                       ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 48557 │\n", "│ <EMAIL>;ana.ma… ┆ 11    │\n", "│ <PERSON>. <PERSON>              ┆ 6     │\n", "│ Dr. <PERSON>              ┆ 5     │\n", "│ Eng.<PERSON>             ┆ 4     │\n", "│ En<PERSON>. <PERSON>                 ┆ 4     │\n", "│ Dr. <PERSON>              ┆ 4     │\n", "│ <PERSON><PERSON>              ┆ 4     │\n", "│ Eng.º <PERSON><PERSON><PERSON>              ┆ 4     │\n", "│ <PERSON><PERSON>              ┆ 4     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: <PERSON><PERSON><PERSON>\n", "Unique values: 3\n", "Top 10 most frequent values:\n", "shape: (3, 2)\n", "┌─────────────────────┬───────┐\n", "│ Reminder Terms Code ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ null                ┆ 47051 │\n", "│ BULK                ┆ 1710  │\n", "│ BULK GA             ┆ 39    │\n", "└─────────────────────┴───────┘\n", "\n", "Column: No_ Series\n", "Unique values: 2213\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────┬───────┐\n", "│ Fax No_   ┆ count │\n", "│ ---       ┆ ---   │\n", "│ str       ┆ u32   │\n", "╞═══════════╪═══════╡\n", "│ null      ┆ 44868 │\n", "│ 291923395 ┆ 74    │\n", "│ 220932544 ┆ 25    │\n", "│ 241372982 ┆ 11    │\n", "│ 259348631 ┆ 11    │\n", "│ 296205359 ┆ 9     │\n", "│ 282342928 ┆ 9     │\n", "│ 275771331 ┆ 8     │\n", "│ 289398309 ┆ 7     │\n", "│ 282341969 ┆ 6     │\n", "└───────────┴───────┘\n", "\n", "Column: Telex Answer Back\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────────┬───────┐\n", "│ Telex Answer Back ┆ count │\n", "│ ---               ┆ ---   │\n", "│ str               ┆ u32   │\n", "╞═══════════════════╪═══════╡\n", "│ null              ┆ 48800 │\n", "└───────────────────┴───────┘\n", "\n", "Column: Gen_ Bus_ Posting Group\n", "Unique values: 6\n", "Top 10 most frequent values:\n", "shape: (6, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Gen_ Bus_ Posting Group ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ NAC                     ┆ 44820 │\n", "│ ACORES                  ┆ 2183  │\n", "│ null                    ┆ 1151  │\n", "│ MADEIRA                 ┆ 603   │\n", "│ OUT                     ┆ 25    │\n", "│ UE                      ┆ 18    │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Picture\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────┬───────┐\n", "│ Picture ┆ count │\n", "│ ---     ┆ ---   │\n", "│ str     ┆ u32   │\n", "╞═════════╪═══════╡\n", "│ null    ┆ 48800 │\n", "└─────────┴───────┘\n", "\n", "Column: GLN\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌──────┬───────┐\n", "│ GLN  ┆ count │\n", "│ ---  ┆ ---   │\n", "│ str  ┆ u32   │\n", "╞══════╪═══════╡\n", "│ null ┆ 48800 │\n", "└──────┴───────┘\n", "\n", "Column: Post Code\n", "Unique values: 11603\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────┬───────┐\n", "│ Post Code ┆ count │\n", "│ ---       ┆ ---   │\n", "│ str       ┆ u32   │\n", "╞═══════════╪═══════╡\n", "│ 8800-216  ┆ 389   │\n", "│ 9500-374  ┆ 377   │\n", "│ 7520-313  ┆ 346   │\n", "│ 8600-781  ┆ 252   │\n", "│ 0000-000  ┆ 241   │\n", "│ 8800-608  ┆ 198   │\n", "│ 8400-141  ┆ 192   │\n", "│ 4950-855  ┆ 180   │\n", "│ 8800-600  ┆ 178   │\n", "│ 2135-042  ┆ 171   │\n", "└───────────┴───────┘\n", "\n", "Column: County\n", "Unique values: 170\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────────────┬───────┐\n", "│ County             ┆ count │\n", "│ ---                ┆ ---   │\n", "│ str                ┆ u32   │\n", "╞════════════════════╪═══════╡\n", "│ Faro               ┆ 8899  │\n", "│ Porto              ┆ 6027  │\n", "│ Lisboa             ┆ 3800  │\n", "│ Leiria             ┆ 3670  │\n", "│ Braga              ┆ 2772  │\n", "│ Aveiro             ┆ 2750  │\n", "│ Viana do Castelo   ┆ 2455  │\n", "│ FARO               ┆ 1931  │\n", "│ <PERSON><PERSON>bal            ┆ 1766  │\n", "│ Ilha de São Miguel ┆ 1743  │\n", "└────────────────────┴───────┘\n", "\n", "Column: E-Mail\n", "Unique values: 8\n", "Top 10 most frequent values:\n", "shape: (8, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ E-Mail                          ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 48791 │\n", "│ s.vasco<PERSON><PERSON>@rubisenergia.pt   ┆ 3     │\n", "│ <EMAIL>    ┆ 1     │\n", "│ <EMAIL>     ┆ 1     │\n", "│ <EMAIL>       ┆ 1     │\n", "│ <EMAIL>          ┆ 1     │\n", "│ casadas<PERSON><PERSON>@gmail.com         ┆ 1     │\n", "│ filipe.rod<PERSON>ues-adh@mosquetei… ┆ 1     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: <PERSON> Page\n", "Unique values: 127\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Home Page                       ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 48557 │\n", "│ <EMAIL>;ana.ma… ┆ 11    │\n", "│ <PERSON>. <PERSON>              ┆ 6     │\n", "│ Dr. <PERSON>              ┆ 5     │\n", "│ Eng.<PERSON>             ┆ 4     │\n", "│ En<PERSON>. <PERSON>                 ┆ 4     │\n", "│ Dr. <PERSON>              ┆ 4     │\n", "│ <PERSON><PERSON>              ┆ 4     │\n", "│ Eng.º <PERSON><PERSON><PERSON>              ┆ 4     │\n", "│ <PERSON><PERSON>              ┆ 4     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: <PERSON><PERSON><PERSON>\n", "Unique values: 3\n", "Top 10 most frequent values:\n", "shape: (3, 2)\n", "┌─────────────────────┬───────┐\n", "│ Reminder Terms Code ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ null                ┆ 47051 │\n", "│ BULK                ┆ 1710  │\n", "│ BULK GA             ┆ 39    │\n", "└─────────────────────┴───────┘\n", "\n", "Column: No_ Series\n", "Unique values: 3\n", "Top 10 most frequent values:\n", "shape: (3, 2)\n", "┌────────────┬───────┐\n", "│ No_ Series ┆ count │\n", "│ ---        ┆ ---   │\n", "│ str        ┆ u32   │\n", "╞════════════╪═══════╡\n", "│ null       ┆ 38044 │\n", "│ CLICAN     ┆ 5819  │\n", "│ CLI        ┆ 4937  │\n", "└────────────┴───────┘\n", "\n", "Column: Tax Area Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────┬───────┐\n", "│ Tax Area Code ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 48800 │\n", "└───────────────┴───────┘\n", "\n", "Column: VAT Bus_ Posting Group\n", "Unique values: 6\n", "Top 10 most frequent values:\n", "shape: (6, 2)\n", "┌────────────────────────┬───────┐\n", "│ VAT Bus_ Posting Group ┆ count │\n", "│ ---                    ┆ ---   │\n", "│ str                    ┆ u32   │\n", "╞════════════════════════╪═══════╡\n", "│ NAC                    ┆ 44822 │\n", "│ ACORES2                ┆ 2183  │\n", "│ null                   ┆ 1149  │\n", "│ MADEIRA                ┆ 603   │\n", "│ OUT                    ┆ 25    │\n", "│ UE                     ┆ 18    │\n", "└────────────────────────┴───────┘\n", "\n", "Column: IC Partner Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────┬───────┐\n", "│ IC Partner Code ┆ count │\n", "│ ---             ┆ ---   │\n", "│ str             ┆ u32   │\n", "╞═════════════════╪═══════╡\n", "│ null            ┆ 48800 │\n", "└─────────────────┴───────┘\n", "\n", "Column: Image\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Image                           ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ ********-0000-0000-0000-000000… ┆ 48800 │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: Preferred Bank Account Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────────┬───────┐\n", "│ Preferred Bank Account Code ┆ count │\n", "│ ---                         ┆ ---   │\n", "│ str                         ┆ u32   │\n", "╞═════════════════════════════╪═══════╡\n", "│ null                        ┆ 48800 │\n", "└─────────────────────────────┴───────┘\n", "\n", "Column: Cash Flow Payment Terms Code\n", "Unique values: 5\n", "Top 10 most frequent values:\n", "shape: (5, 2)\n", "┌──────────────────────────────┬───────┐\n", "│ Cash Flow Payment Terms Code ┆ count │\n", "│ ---                          ┆ ---   │\n", "│ str                          ┆ u32   │\n", "╞══════════════════════════════╪═══════╡\n", "│ null                         ┆ 48795 │\n", "│ C408                         ┆ 2     │\n", "│ C406                         ┆ 1     │\n", "│ C158                         ┆ 1     │\n", "│ C093                         ┆ 1     │\n", "└──────────────────────────────┴───────┘\n", "\n", "Column: Primary Contact No_\n", "Unique values: 2364\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────┬───────┐\n", "│ Primary Contact No_ ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ null                ┆ 46437 │\n", "│ C00023510           ┆ 1     │\n", "│ C00021764           ┆ 1     │\n", "│ C00046789           ┆ 1     │\n", "│ C00044930           ┆ 1     │\n", "│ C00054716           ┆ 1     │\n", "│ C00045301           ┆ 1     │\n", "│ C00046250           ┆ 1     │\n", "│ C00018453           ┆ 1     │\n", "│ C00021503           ┆ 1     │\n", "└─────────────────────┴───────┘\n", "\n", "Column: Responsibility Center\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────────────┬───────┐\n", "│ Responsibility Center ┆ count │\n", "│ ---                   ┆ ---   │\n", "│ str                   ┆ u32   │\n", "╞═══════════════════════╪═══════╡\n", "│ null                  ┆ 48800 │\n", "└───────────────────────┴───────┘\n", "\n", "Column: Shipping Time\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────┬───────┐\n", "│ Shipping Time ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 48800 │\n", "└───────────────┴───────┘\n", "\n", "Column: Shipping Agent Service Code\n", "Unique values: 3\n", "Top 10 most frequent values:\n", "shape: (3, 2)\n", "┌────────────┬───────┐\n", "│ No_ Series ┆ count │\n", "│ ---        ┆ ---   │\n", "│ str        ┆ u32   │\n", "╞════════════╪═══════╡\n", "│ null       ┆ 38044 │\n", "│ CLICAN     ┆ 5819  │\n", "│ CLI        ┆ 4937  │\n", "└────────────┴───────┘\n", "\n", "Column: Tax Area Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────┬───────┐\n", "│ Tax Area Code ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 48800 │\n", "└───────────────┴───────┘\n", "\n", "Column: VAT Bus_ Posting Group\n", "Unique values: 6\n", "Top 10 most frequent values:\n", "shape: (6, 2)\n", "┌────────────────────────┬───────┐\n", "│ VAT Bus_ Posting Group ┆ count │\n", "│ ---                    ┆ ---   │\n", "│ str                    ┆ u32   │\n", "╞════════════════════════╪═══════╡\n", "│ NAC                    ┆ 44822 │\n", "│ ACORES2                ┆ 2183  │\n", "│ null                   ┆ 1149  │\n", "│ MADEIRA                ┆ 603   │\n", "│ OUT                    ┆ 25    │\n", "│ UE                     ┆ 18    │\n", "└────────────────────────┴───────┘\n", "\n", "Column: IC Partner Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────┬───────┐\n", "│ IC Partner Code ┆ count │\n", "│ ---             ┆ ---   │\n", "│ str             ┆ u32   │\n", "╞═════════════════╪═══════╡\n", "│ null            ┆ 48800 │\n", "└─────────────────┴───────┘\n", "\n", "Column: Image\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Image                           ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ ********-0000-0000-0000-000000… ┆ 48800 │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: Preferred Bank Account Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────────┬───────┐\n", "│ Preferred Bank Account Code ┆ count │\n", "│ ---                         ┆ ---   │\n", "│ str                         ┆ u32   │\n", "╞═════════════════════════════╪═══════╡\n", "│ null                        ┆ 48800 │\n", "└─────────────────────────────┴───────┘\n", "\n", "Column: Cash Flow Payment Terms Code\n", "Unique values: 5\n", "Top 10 most frequent values:\n", "shape: (5, 2)\n", "┌──────────────────────────────┬───────┐\n", "│ Cash Flow Payment Terms Code ┆ count │\n", "│ ---                          ┆ ---   │\n", "│ str                          ┆ u32   │\n", "╞══════════════════════════════╪═══════╡\n", "│ null                         ┆ 48795 │\n", "│ C408                         ┆ 2     │\n", "│ C406                         ┆ 1     │\n", "│ C158                         ┆ 1     │\n", "│ C093                         ┆ 1     │\n", "└──────────────────────────────┴───────┘\n", "\n", "Column: Primary Contact No_\n", "Unique values: 2364\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────┬───────┐\n", "│ Primary Contact No_ ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ null                ┆ 46437 │\n", "│ C00023510           ┆ 1     │\n", "│ C00021764           ┆ 1     │\n", "│ C00046789           ┆ 1     │\n", "│ C00044930           ┆ 1     │\n", "│ C00054716           ┆ 1     │\n", "│ C00045301           ┆ 1     │\n", "│ C00046250           ┆ 1     │\n", "│ C00018453           ┆ 1     │\n", "│ C00021503           ┆ 1     │\n", "└─────────────────────┴───────┘\n", "\n", "Column: Responsibility Center\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────────────┬───────┐\n", "│ Responsibility Center ┆ count │\n", "│ ---                   ┆ ---   │\n", "│ str                   ┆ u32   │\n", "╞═══════════════════════╪═══════╡\n", "│ null                  ┆ 48800 │\n", "└───────────────────────┴───────┘\n", "\n", "Column: Shipping Time\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────┬───────┐\n", "│ Shipping Time ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 48800 │\n", "└───────────────┴───────┘\n", "\n", "Column: Shipping Agent Service Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────────┬───────┐\n", "│ Shipping Agent Service Code ┆ count │\n", "│ ---                         ┆ ---   │\n", "│ str                         ┆ u32   │\n", "╞═════════════════════════════╪═══════╡\n", "│ null                        ┆ 48800 │\n", "└─────────────────────────────┴───────┘\n", "\n", "Column: Service Zone Code\n", "Unique values: 26\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────────────┬───────┐\n", "│ Service Zone Code ┆ count │\n", "│ ---               ┆ ---   │\n", "│ str               ┆ u32   │\n", "╞═══════════════════╪═══════╡\n", "│ null              ┆ 34793 │\n", "│ 08                ┆ 3114  │\n", "│ 13                ┆ 1364  │\n", "│ 11                ┆ 1286  │\n", "│ 03                ┆ 1065  │\n", "│ 01                ┆ 949   │\n", "│ 60                ┆ 734   │\n", "│ 15                ┆ 707   │\n", "│ 14                ┆ 664   │\n", "│ 10                ┆ 661   │\n", "└───────────────────┴───────┘\n", "\n", "Column: Base Calendar Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────────────┬───────┐\n", "│ Base Calendar Code ┆ count │\n", "│ ---                ┆ ---   │\n", "│ str                ┆ u32   │\n", "╞════════════════════╪═══════╡\n", "│ null               ┆ 48800 │\n", "└────────────────────┴───────┘\n", "\n", "Column: Commission Agent\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌──────────────────┬───────┐\n", "│ Commission Agent ┆ count │\n", "│ ---              ┆ ---   │\n", "│ str              ┆ u32   │\n", "╞══════════════════╪═══════╡\n", "│ null             ┆ 48800 │\n", "└──────────────────┴───────┘\n", "\n", "Column: Mnemonic\n", "Unique values: 6526\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────┬───────┐\n", "│ Mnemonic   ┆ count │\n", "│ ---        ┆ ---   │\n", "│ str        ┆ u32   │\n", "╞════════════╪═══════╡\n", "│ null       ┆ 42197 │\n", "│ REPSOLGAS  ┆ 5     │\n", "│ DIDAXLISBG ┆ 3     │\n", "│ FASTETEJBG ┆ 3     │\n", "│ RMARIAVMBG ┆ 2     │\n", "│ XINVELOUBG ┆ 2     │\n", "│ THSPIIANBG ┆ 2     │\n", "│ SFBRESBABG ┆ 2     │\n", "│ GASCABENBG ┆ 2     │\n", "│ MGINVQUABG ┆ 2     │\n", "└────────────┴───────┘\n", "\n", "Column: Delivery Remarks\n", "Unique values: 348\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Delivery Remarks                ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 48102 │\n", "│ DI Viseu                        ┆ 156   │\n", "│ Autogás                         ┆ 46    │\n", "│ AUTOGAS                         ┆ 40    │\n", "│ Cliente isento                  ┆ 35    │\n", "│ Cliente facturado pelo peso da… ┆ 12    │\n", "│ GPL FL                          ┆ 6     │\n", "│ cliente bascula                 ┆ 4     │\n", "│ Abastecer só 1 a 50%            ┆ 4     │\n", "│ CARRO 497 NÃO FAZ               ┆ 4     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: GPS Longitude\n", "Unique values: 6504\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────────┬───────┐\n", "│ GPS Longitude ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 41435 │\n", "│ -8.2227       ┆ 5     │\n", "│ -8.813654     ┆ 4     │\n", "│ -8.74436      ┆ 4     │\n", "│ -9.36602      ┆ 4     │\n", "│ -8.64925      ┆ 4     │\n", "│ -7.646337     ┆ 4     │\n", "│ -8.372778     ┆ 4     │\n", "│ -8.04715      ┆ 4     │\n", "│ -8.23668      ┆ 4     │\n", "└───────────────┴───────┘\n", "\n", "Column: Mobile Phone\n", "Unique values: 2884\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────┬───────┐\n", "│ Mobile Phone ┆ count │\n", "│ ---          ┆ ---   │\n", "│ str          ┆ u32   │\n", "╞══════════════╪═══════╡\n", "│ null         ┆ 45018 │\n", "│ 919696059    ┆ 46    │\n", "│ 912223569    ┆ 27    │\n", "│ 296559277    ┆ 12    │\n", "│ 918945801    ┆ 10    │\n", "│ 912025074    ┆ 6     │\n", "│ 918792311    ┆ 5     │\n", "│ 962467898    ┆ 5     │\n", "│ 296385904    ┆ 5     │\n", "│ 917582398    ┆ 5     │\n", "└──────────────┴───────┘\n", "\n", "Column: ADC Limit Date\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────┬───────┐\n", "│ ADC Limit Date          ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 1753-01-01 00:00:00.000 ┆ 48800 │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Old ADC\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────┬───────┐\n", "│ Old ADC ┆ count │\n", "│ ---     ┆ ---   │\n", "│ str     ┆ u32   │\n", "╞═════════╪═══════╡\n", "│ null    ┆ 48800 │\n", "└─────────┴───────┘\n", "\n", "Column: Customer Location Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────────┬───────┐\n", "│ Shipping Agent Service Code ┆ count │\n", "│ ---                         ┆ ---   │\n", "│ str                         ┆ u32   │\n", "╞═════════════════════════════╪═══════╡\n", "│ null                        ┆ 48800 │\n", "└─────────────────────────────┴───────┘\n", "\n", "Column: Service Zone Code\n", "Unique values: 26\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────────────┬───────┐\n", "│ Service Zone Code ┆ count │\n", "│ ---               ┆ ---   │\n", "│ str               ┆ u32   │\n", "╞═══════════════════╪═══════╡\n", "│ null              ┆ 34793 │\n", "│ 08                ┆ 3114  │\n", "│ 13                ┆ 1364  │\n", "│ 11                ┆ 1286  │\n", "│ 03                ┆ 1065  │\n", "│ 01                ┆ 949   │\n", "│ 60                ┆ 734   │\n", "│ 15                ┆ 707   │\n", "│ 14                ┆ 664   │\n", "│ 10                ┆ 661   │\n", "└───────────────────┴───────┘\n", "\n", "Column: Base Calendar Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────────────┬───────┐\n", "│ Base Calendar Code ┆ count │\n", "│ ---                ┆ ---   │\n", "│ str                ┆ u32   │\n", "╞════════════════════╪═══════╡\n", "│ null               ┆ 48800 │\n", "└────────────────────┴───────┘\n", "\n", "Column: Commission Agent\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌──────────────────┬───────┐\n", "│ Commission Agent ┆ count │\n", "│ ---              ┆ ---   │\n", "│ str              ┆ u32   │\n", "╞══════════════════╪═══════╡\n", "│ null             ┆ 48800 │\n", "└──────────────────┴───────┘\n", "\n", "Column: Mnemonic\n", "Unique values: 6526\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────┬───────┐\n", "│ Mnemonic   ┆ count │\n", "│ ---        ┆ ---   │\n", "│ str        ┆ u32   │\n", "╞════════════╪═══════╡\n", "│ null       ┆ 42197 │\n", "│ REPSOLGAS  ┆ 5     │\n", "│ DIDAXLISBG ┆ 3     │\n", "│ FASTETEJBG ┆ 3     │\n", "│ RMARIAVMBG ┆ 2     │\n", "│ XINVELOUBG ┆ 2     │\n", "│ THSPIIANBG ┆ 2     │\n", "│ SFBRESBABG ┆ 2     │\n", "│ GASCABENBG ┆ 2     │\n", "│ MGINVQUABG ┆ 2     │\n", "└────────────┴───────┘\n", "\n", "Column: Delivery Remarks\n", "Unique values: 348\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Delivery Remarks                ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 48102 │\n", "│ DI Viseu                        ┆ 156   │\n", "│ Autogás                         ┆ 46    │\n", "│ AUTOGAS                         ┆ 40    │\n", "│ Cliente isento                  ┆ 35    │\n", "│ Cliente facturado pelo peso da… ┆ 12    │\n", "│ GPL FL                          ┆ 6     │\n", "│ cliente bascula                 ┆ 4     │\n", "│ Abastecer só 1 a 50%            ┆ 4     │\n", "│ CARRO 497 NÃO FAZ               ┆ 4     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: GPS Longitude\n", "Unique values: 6504\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────────┬───────┐\n", "│ GPS Longitude ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 41435 │\n", "│ -8.2227       ┆ 5     │\n", "│ -8.813654     ┆ 4     │\n", "│ -8.74436      ┆ 4     │\n", "│ -9.36602      ┆ 4     │\n", "│ -8.64925      ┆ 4     │\n", "│ -7.646337     ┆ 4     │\n", "│ -8.372778     ┆ 4     │\n", "│ -8.04715      ┆ 4     │\n", "│ -8.23668      ┆ 4     │\n", "└───────────────┴───────┘\n", "\n", "Column: Mobile Phone\n", "Unique values: 2884\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────┬───────┐\n", "│ Mobile Phone ┆ count │\n", "│ ---          ┆ ---   │\n", "│ str          ┆ u32   │\n", "╞══════════════╪═══════╡\n", "│ null         ┆ 45018 │\n", "│ 919696059    ┆ 46    │\n", "│ 912223569    ┆ 27    │\n", "│ 296559277    ┆ 12    │\n", "│ 918945801    ┆ 10    │\n", "│ 912025074    ┆ 6     │\n", "│ 918792311    ┆ 5     │\n", "│ 962467898    ┆ 5     │\n", "│ 296385904    ┆ 5     │\n", "│ 917582398    ┆ 5     │\n", "└──────────────┴───────┘\n", "\n", "Column: ADC Limit Date\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────┬───────┐\n", "│ ADC Limit Date          ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 1753-01-01 00:00:00.000 ┆ 48800 │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Old ADC\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────┬───────┐\n", "│ Old ADC ┆ count │\n", "│ ---     ┆ ---   │\n", "│ str     ┆ u32   │\n", "╞═════════╪═══════╡\n", "│ null    ┆ 48800 │\n", "└─────────┴───────┘\n", "\n", "Column: Customer Location Code\n", "Unique values: 518\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────────────────┬───────┐\n", "│ Customer Location Code ┆ count │\n", "│ ---                    ┆ ---   │\n", "│ str                    ┆ u32   │\n", "╞════════════════════════╪═══════╡\n", "│ null                   ┆ 34820 │\n", "│ ECAN0000               ┆ 3219  │\n", "│ 5088                   ┆ 366   │\n", "│ GCAN1802               ┆ 311   │\n", "│ DI                     ┆ 303   │\n", "│ GCAN1809               ┆ 284   │\n", "│ GCAN1234               ┆ 218   │\n", "│ GCAN1101               ┆ 175   │\n", "│ GCAN1031               ┆ 145   │\n", "│ GCAN1149               ┆ 127   │\n", "└────────────────────────┴───────┘\n", "\n", "Column: SDD Adherence Date\n", "Unique values: 1014\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ SDD Adherence Date      ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 1753-01-01 00:00:00.000 ┆ 45253 │\n", "│ 2019-02-12 00:00:00.000 ┆ 479   │\n", "│ 2014-07-24 00:00:00.000 ┆ 58    │\n", "│ 2022-03-17 00:00:00.000 ┆ 49    │\n", "│ 2022-03-16 00:00:00.000 ┆ 39    │\n", "│ 2023-10-20 00:00:00.000 ┆ 23    │\n", "│ 2023-09-20 00:00:00.000 ┆ 22    │\n", "│ 2023-11-21 00:00:00.000 ┆ 21    │\n", "│ 2024-10-21 00:00:00.000 ┆ 20    │\n", "│ 2024-11-21 00:00:00.000 ┆ 20    │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Exemption No_\n", "Unique values: 56\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────────────────┬───────┐\n", "│ Exemption No_                ┆ count │\n", "│ ---                          ┆ ---   │\n", "│ str                          ┆ u32   │\n", "╞══════════════════════════════╪═══════╡\n", "│ null                         ┆ 48741 │\n", "│ 2024/0000752_31/12/2030      ┆ 2     │\n", "│ trans intracomunitária       ┆ 2     │\n", "│ 2019/0001953_31/12/2025      ┆ 2     │\n", "│ 2019/0001040_31/12/2025      ┆ 2     │\n", "│ 2013/0002399_ARCE 00/00/0000 ┆ 1     │\n", "│ Produto Cativo               ┆ 1     │\n", "│ 2024/0001872_31/12/2030      ┆ 1     │\n", "│ Exportação Canárias          ┆ 1     │\n", "│ 2014/0004987_31/12/2025      ┆ 1     │\n", "└──────────────────────────────┴───────┘\n", "\n", "Column: <PERSON><PERSON><PERSON> por\n", "Unique values: 63\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────┬───────┐\n", "│ <PERSON><PERSON><PERSON> por       ┆ count │\n", "│ ---              ┆ ---   │\n", "│ str              ┆ u32   │\n", "╞══════════════════╪═══════╡\n", "│ null             ┆ 14530 │\n", "│ REP\\BMHYDRA      ┆ 14313 │\n", "│ REP\\JBATISTA     ┆ 7608  │\n", "│ REP\\FCAPELAS     ┆ 4369  │\n", "│ REP\\CALVES       ┆ 1045  │\n", "│ REP\\MCASMARRINHA ┆ 740   │\n", "│ REP\\HCHYDRA      ┆ 724   │\n", "│ REP\\MREIS        ┆ 618   │\n", "│ REP\\SUPNAV       ┆ 491   │\n", "│ REP\\CDIAS        ┆ 482   │\n", "└──────────────────┴───────┘\n", "\n", "Column: Start Date\n", "Unique values: 1171\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Start Date              ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 1753-01-01 00:00:00.000 ┆ 45054 │\n", "│ 2024-01-01 00:00:00.000 ┆ 56    │\n", "│ 2023-06-01 00:00:00.000 ┆ 55    │\n", "│ 2016-05-01 00:00:00.000 ┆ 44    │\n", "│ 2023-01-01 00:00:00.000 ┆ 41    │\n", "│ 2021-11-01 00:00:00.000 ┆ 40    │\n", "│ 2021-03-01 00:00:00.000 ┆ 38    │\n", "│ 2022-01-01 00:00:00.000 ┆ 34    │\n", "│ 2021-01-01 00:00:00.000 ┆ 32    │\n", "│ 2019-07-01 00:00:00.000 ┆ 31    │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: End Date\n", "Unique values: 1027\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ End Date                ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 1753-01-01 00:00:00.000 ┆ 45535 │\n", "│ 2023-12-31 00:00:00.000 ┆ 44    │\n", "│ 2024-12-31 00:00:00.000 ┆ 42    │\n", "│ 2026-02-28 00:00:00.000 ┆ 37    │\n", "│ 2017-04-30 00:00:00.000 ┆ 37    │\n", "│ 2022-12-31 00:00:00.000 ┆ 35    │\n", "│ 2026-12-31 00:00:00.000 ┆ 35    │\n", "│ 2024-10-31 00:00:00.000 ┆ 31    │\n", "│ 2024-06-30 00:00:00.000 ┆ 30    │\n", "│ 2028-12-31 00:00:00.000 ┆ 29    │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Shortcut Dimension 6\n", "Unique values: 20\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────────┬───────┐\n", "│ Shortcut Dimension 6 ┆ count │\n", "│ ---                  ┆ ---   │\n", "│ str                  ┆ u32   │\n", "╞══════════════════════╪═══════╡\n", "│ null                 ┆ 34269 │\n", "│ T                    ┆ 3659  │\n", "│ C                    ┆ 2639  │\n", "│ I                    ┆ 2155  │\n", "│ D                    ┆ 1374  │\n", "│ G                    ┆ 1330  │\n", "│ Q                    ┆ 1295  │\n", "│ O                    ┆ 593   │\n", "│ A                    ┆ 556   │\n", "│ S                    ┆ 402   │\n", "└──────────────────────┴───────┘\n", "\n", "Column: Shortcut Dimension 7\n", "Unique values: 19\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────────┬───────┐\n", "│ Shortcut Dimension 7 ┆ count │\n", "│ ---                  ┆ ---   │\n", "│ str                  ┆ u32   │\n", "╞══════════════════════╪═══════╡\n", "│ null                 ┆ 48458 │\n", "│ DI006                ┆ 65    │\n", "│ DI008                ┆ 32    │\n", "│ DI005                ┆ 31    │\n", "│ DI011                ┆ 30    │\n", "│ DI019                ┆ 28    │\n", "│ DI012                ┆ 20    │\n", "│ DI018                ┆ 16    │\n", "│ DI009                ┆ 14    │\n", "│ DI015                ┆ 14    │\n", "└──────────────────────┴───────┘\n", "\n", "Column: E-Mail Precos\n", "Unique values: 518\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────────────────┬───────┐\n", "│ Customer Location Code ┆ count │\n", "│ ---                    ┆ ---   │\n", "│ str                    ┆ u32   │\n", "╞════════════════════════╪═══════╡\n", "│ null                   ┆ 34820 │\n", "│ ECAN0000               ┆ 3219  │\n", "│ 5088                   ┆ 366   │\n", "│ GCAN1802               ┆ 311   │\n", "│ DI                     ┆ 303   │\n", "│ GCAN1809               ┆ 284   │\n", "│ GCAN1234               ┆ 218   │\n", "│ GCAN1101               ┆ 175   │\n", "│ GCAN1031               ┆ 145   │\n", "│ GCAN1149               ┆ 127   │\n", "└────────────────────────┴───────┘\n", "\n", "Column: SDD Adherence Date\n", "Unique values: 1014\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ SDD Adherence Date      ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 1753-01-01 00:00:00.000 ┆ 45253 │\n", "│ 2019-02-12 00:00:00.000 ┆ 479   │\n", "│ 2014-07-24 00:00:00.000 ┆ 58    │\n", "│ 2022-03-17 00:00:00.000 ┆ 49    │\n", "│ 2022-03-16 00:00:00.000 ┆ 39    │\n", "│ 2023-10-20 00:00:00.000 ┆ 23    │\n", "│ 2023-09-20 00:00:00.000 ┆ 22    │\n", "│ 2023-11-21 00:00:00.000 ┆ 21    │\n", "│ 2024-10-21 00:00:00.000 ┆ 20    │\n", "│ 2024-11-21 00:00:00.000 ┆ 20    │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Exemption No_\n", "Unique values: 56\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────────────────┬───────┐\n", "│ Exemption No_                ┆ count │\n", "│ ---                          ┆ ---   │\n", "│ str                          ┆ u32   │\n", "╞══════════════════════════════╪═══════╡\n", "│ null                         ┆ 48741 │\n", "│ 2024/0000752_31/12/2030      ┆ 2     │\n", "│ trans intracomunitária       ┆ 2     │\n", "│ 2019/0001953_31/12/2025      ┆ 2     │\n", "│ 2019/0001040_31/12/2025      ┆ 2     │\n", "│ 2013/0002399_ARCE 00/00/0000 ┆ 1     │\n", "│ Produto Cativo               ┆ 1     │\n", "│ 2024/0001872_31/12/2030      ┆ 1     │\n", "│ Exportação Canárias          ┆ 1     │\n", "│ 2014/0004987_31/12/2025      ┆ 1     │\n", "└──────────────────────────────┴───────┘\n", "\n", "Column: <PERSON><PERSON><PERSON> por\n", "Unique values: 63\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────┬───────┐\n", "│ <PERSON><PERSON><PERSON> por       ┆ count │\n", "│ ---              ┆ ---   │\n", "│ str              ┆ u32   │\n", "╞══════════════════╪═══════╡\n", "│ null             ┆ 14530 │\n", "│ REP\\BMHYDRA      ┆ 14313 │\n", "│ REP\\JBATISTA     ┆ 7608  │\n", "│ REP\\FCAPELAS     ┆ 4369  │\n", "│ REP\\CALVES       ┆ 1045  │\n", "│ REP\\MCASMARRINHA ┆ 740   │\n", "│ REP\\HCHYDRA      ┆ 724   │\n", "│ REP\\MREIS        ┆ 618   │\n", "│ REP\\SUPNAV       ┆ 491   │\n", "│ REP\\CDIAS        ┆ 482   │\n", "└──────────────────┴───────┘\n", "\n", "Column: Start Date\n", "Unique values: 1171\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Start Date              ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 1753-01-01 00:00:00.000 ┆ 45054 │\n", "│ 2024-01-01 00:00:00.000 ┆ 56    │\n", "│ 2023-06-01 00:00:00.000 ┆ 55    │\n", "│ 2016-05-01 00:00:00.000 ┆ 44    │\n", "│ 2023-01-01 00:00:00.000 ┆ 41    │\n", "│ 2021-11-01 00:00:00.000 ┆ 40    │\n", "│ 2021-03-01 00:00:00.000 ┆ 38    │\n", "│ 2022-01-01 00:00:00.000 ┆ 34    │\n", "│ 2021-01-01 00:00:00.000 ┆ 32    │\n", "│ 2019-07-01 00:00:00.000 ┆ 31    │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: End Date\n", "Unique values: 1027\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ End Date                ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 1753-01-01 00:00:00.000 ┆ 45535 │\n", "│ 2023-12-31 00:00:00.000 ┆ 44    │\n", "│ 2024-12-31 00:00:00.000 ┆ 42    │\n", "│ 2026-02-28 00:00:00.000 ┆ 37    │\n", "│ 2017-04-30 00:00:00.000 ┆ 37    │\n", "│ 2022-12-31 00:00:00.000 ┆ 35    │\n", "│ 2026-12-31 00:00:00.000 ┆ 35    │\n", "│ 2024-10-31 00:00:00.000 ┆ 31    │\n", "│ 2024-06-30 00:00:00.000 ┆ 30    │\n", "│ 2028-12-31 00:00:00.000 ┆ 29    │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Shortcut Dimension 6\n", "Unique values: 20\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────────┬───────┐\n", "│ Shortcut Dimension 6 ┆ count │\n", "│ ---                  ┆ ---   │\n", "│ str                  ┆ u32   │\n", "╞══════════════════════╪═══════╡\n", "│ null                 ┆ 34269 │\n", "│ T                    ┆ 3659  │\n", "│ C                    ┆ 2639  │\n", "│ I                    ┆ 2155  │\n", "│ D                    ┆ 1374  │\n", "│ G                    ┆ 1330  │\n", "│ Q                    ┆ 1295  │\n", "│ O                    ┆ 593   │\n", "│ A                    ┆ 556   │\n", "│ S                    ┆ 402   │\n", "└──────────────────────┴───────┘\n", "\n", "Column: Shortcut Dimension 7\n", "Unique values: 19\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────────┬───────┐\n", "│ Shortcut Dimension 7 ┆ count │\n", "│ ---                  ┆ ---   │\n", "│ str                  ┆ u32   │\n", "╞══════════════════════╪═══════╡\n", "│ null                 ┆ 48458 │\n", "│ DI006                ┆ 65    │\n", "│ DI008                ┆ 32    │\n", "│ DI005                ┆ 31    │\n", "│ DI011                ┆ 30    │\n", "│ DI019                ┆ 28    │\n", "│ DI012                ┆ 20    │\n", "│ DI018                ┆ 16    │\n", "│ DI009                ┆ 14    │\n", "│ DI015                ┆ 14    │\n", "└──────────────────────┴───────┘\n", "\n", "Column: E-Mail Precos\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────┬───────┐\n", "│ E-Mail Precos ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 48800 │\n", "└───────────────┴───────┘\n", "\n", "Column: Email Daily Deliveries\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────────────────┬───────┐\n", "│ Email Daily Deliveries ┆ count │\n", "│ ---                    ┆ ---   │\n", "│ str                    ┆ u32   │\n", "╞════════════════════════╪═══════╡\n", "│ null                   ┆ 48800 │\n", "└────────────────────────┴───────┘\n", "\n", "Column: Customer Bulk_Packed CCC No_\n", "Unique values: 2\n", "Top 10 most frequent values:\n", "shape: (2, 2)\n", "┌──────────────────────────────┬───────┐\n", "│ Customer Bulk_Packed CCC No_ ┆ count │\n", "│ ---                          ┆ ---   │\n", "│ str                          ┆ u32   │\n", "╞══════════════════════════════╪═══════╡\n", "│ null                         ┆ 48799 │\n", "│ 001800031141989802410        ┆ 1     │\n", "└──────────────────────────────┴───────┘\n", "\n", "Column: Customer Bulk_Packed IBAN\n", "Unique values: 17\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────────────────────┬───────┐\n", "│ Customer Bulk_Packed IBAN ┆ count │\n", "│ ---                       ┆ ---   │\n", "│ str                       ┆ u32   │\n", "╞═══════════════════════════╪═══════╡\n", "│ null                      ┆ 48784 │\n", "│ ************************* ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "│ LPGCUSTOMERSERVICE@RUBISE ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "│ ***********************   ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "└───────────────────────────┴───────┘\n", "\n", "Column: Customer Bulk_Packed SWIFT\n", "Unique values: 9\n", "Top 10 most frequent values:\n", "shape: (9, 2)\n", "┌────────────────────────────┬───────┐\n", "│ Customer Bulk_Packed SWIFT ┆ count │\n", "│ ---                        ┆ ---   │\n", "│ str                        ┆ u32   │\n", "╞════════════════════════════╪═══════╡\n", "│ null                       ┆ 48785 │\n", "│ CGDIPTPL                   ┆ 3     │\n", "│ BCOMPTPL                   ┆ 3     │\n", "│ CCCMPTPL                   ┆ 3     │\n", "│ BBPIPTPL                   ┆ 2     │\n", "│ TOTAPTPL                   ┆ 1     │\n", "│ BESCPTPL                   ┆ 1     │\n", "│ BBVAPTPL                   ┆ 1     │\n", "│ ACTVPTPL                   ┆ 1     │\n", "└────────────────────────────┴───────┘\n", "\n", "Column: E-Mail GR\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────┬───────┐\n", "│ E-Mail GR ┆ count │\n", "│ ---       ┆ ---   │\n", "│ str       ┆ u32   │\n", "╞═══════════╪═══════╡\n", "│ null      ┆ 48800 │\n", "└───────────┴───────┘\n", "\n", "Column: Portal User Name\n", "Unique values: 28\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────┬───────┐\n", "│ Portal User Name ┆ count │\n", "│ ---              ┆ ---   │\n", "│ str              ┆ u32   │\n", "╞══════════════════╪═══════╡\n", "│ null             ┆ 48773 │\n", "│ gascombgestvng   ┆ 1     │\n", "│ jmartiniano      ┆ 1     │\n", "│ JNRodriguesST    ┆ 1     │\n", "│ RHCH             ┆ 1     │\n", "│ Lojalila         ┆ 1     │\n", "│ RhchBeja1        ┆ 1     │\n", "│ aradegas         ┆ 1     │\n", "│ Somosgasgdm      ┆ 1     │\n", "│ sduarte          ┆ 1     │\n", "└──────────────────┴───────┘\n", "\n", "Column: LastDateMetacase\n", "Unique values: 1971\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ LastDateMetacase        ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 2025-01-23 00:00:00.000 ┆ 8250  │\n", "│ 2025-01-27 00:00:00.000 ┆ 3689  │\n", "│ 2021-03-15 00:00:00.000 ┆ 3589  │\n", "│ 2025-01-22 00:00:00.000 ┆ 2241  │\n", "│ 2019-01-19 00:00:00.000 ┆ 1765  │\n", "│ 2025-01-21 00:00:00.000 ┆ 1438  │\n", "│ 2022-03-10 00:00:00.000 ┆ 1233  │\n", "│ 2018-03-16 00:00:00.000 ┆ 720   │\n", "│ 2025-01-28 00:00:00.000 ┆ 469   │\n", "│ 2019-02-12 00:00:00.000 ┆ 449   │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: <PERSON><PERSON>\n", "Unique values: 13735\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────┬───────┐\n", "│ Meter      ┆ count │\n", "│ ---        ┆ ---   │\n", "│ str        ┆ u32   │\n", "╞════════════╪═══════╡\n", "│ null       ┆ 35060 │\n", "│ KR6701234  ┆ 2     │\n", "│ NU57505941 ┆ 2     │\n", "│ NU57281468 ┆ 2     │\n", "│ EK5850325  ┆ 2     │\n", "│ GA5258962  ┆ 2     │\n", "│ GA5713594  ┆ 2     │\n", "│ GA3140250  ┆ 1     │\n", "│ EK6339714  ┆ 1     │\n", "│ EK5797933  ┆ 1     │\n", "└────────────┴───────┘\n", "\n", "Column: Social Security No_\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────┬───────┐\n", "│ E-Mail Precos ┆ count │\n", "│ ---           ┆ ---   │\n", "│ str           ┆ u32   │\n", "╞═══════════════╪═══════╡\n", "│ null          ┆ 48800 │\n", "└───────────────┴───────┘\n", "\n", "Column: Email Daily Deliveries\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────────────────┬───────┐\n", "│ Email Daily Deliveries ┆ count │\n", "│ ---                    ┆ ---   │\n", "│ str                    ┆ u32   │\n", "╞════════════════════════╪═══════╡\n", "│ null                   ┆ 48800 │\n", "└────────────────────────┴───────┘\n", "\n", "Column: Customer Bulk_Packed CCC No_\n", "Unique values: 2\n", "Top 10 most frequent values:\n", "shape: (2, 2)\n", "┌──────────────────────────────┬───────┐\n", "│ Customer Bulk_Packed CCC No_ ┆ count │\n", "│ ---                          ┆ ---   │\n", "│ str                          ┆ u32   │\n", "╞══════════════════════════════╪═══════╡\n", "│ null                         ┆ 48799 │\n", "│ 001800031141989802410        ┆ 1     │\n", "└──────────────────────────────┴───────┘\n", "\n", "Column: Customer Bulk_Packed IBAN\n", "Unique values: 17\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────────────────────┬───────┐\n", "│ Customer Bulk_Packed IBAN ┆ count │\n", "│ ---                       ┆ ---   │\n", "│ str                       ┆ u32   │\n", "╞═══════════════════════════╪═══════╡\n", "│ null                      ┆ 48784 │\n", "│ ************************* ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "│ LPGCUSTOMERSERVICE@RUBISE ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "│ ***********************   ┆ 1     │\n", "│ ************************* ┆ 1     │\n", "└───────────────────────────┴───────┘\n", "\n", "Column: Customer Bulk_Packed SWIFT\n", "Unique values: 9\n", "Top 10 most frequent values:\n", "shape: (9, 2)\n", "┌────────────────────────────┬───────┐\n", "│ Customer Bulk_Packed SWIFT ┆ count │\n", "│ ---                        ┆ ---   │\n", "│ str                        ┆ u32   │\n", "╞════════════════════════════╪═══════╡\n", "│ null                       ┆ 48785 │\n", "│ CGDIPTPL                   ┆ 3     │\n", "│ BCOMPTPL                   ┆ 3     │\n", "│ CCCMPTPL                   ┆ 3     │\n", "│ BBPIPTPL                   ┆ 2     │\n", "│ TOTAPTPL                   ┆ 1     │\n", "│ BESCPTPL                   ┆ 1     │\n", "│ BBVAPTPL                   ┆ 1     │\n", "│ ACTVPTPL                   ┆ 1     │\n", "└────────────────────────────┴───────┘\n", "\n", "Column: E-Mail GR\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────┬───────┐\n", "│ E-Mail GR ┆ count │\n", "│ ---       ┆ ---   │\n", "│ str       ┆ u32   │\n", "╞═══════════╪═══════╡\n", "│ null      ┆ 48800 │\n", "└───────────┴───────┘\n", "\n", "Column: Portal User Name\n", "Unique values: 28\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌──────────────────┬───────┐\n", "│ Portal User Name ┆ count │\n", "│ ---              ┆ ---   │\n", "│ str              ┆ u32   │\n", "╞══════════════════╪═══════╡\n", "│ null             ┆ 48773 │\n", "│ gascombgestvng   ┆ 1     │\n", "│ jmartiniano      ┆ 1     │\n", "│ JNRodriguesST    ┆ 1     │\n", "│ RHCH             ┆ 1     │\n", "│ Lojalila         ┆ 1     │\n", "│ RhchBeja1        ┆ 1     │\n", "│ aradegas         ┆ 1     │\n", "│ Somosgasgdm      ┆ 1     │\n", "│ sduarte          ┆ 1     │\n", "└──────────────────┴───────┘\n", "\n", "Column: LastDateMetacase\n", "Unique values: 1971\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ LastDateMetacase        ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 2025-01-23 00:00:00.000 ┆ 8250  │\n", "│ 2025-01-27 00:00:00.000 ┆ 3689  │\n", "│ 2021-03-15 00:00:00.000 ┆ 3589  │\n", "│ 2025-01-22 00:00:00.000 ┆ 2241  │\n", "│ 2019-01-19 00:00:00.000 ┆ 1765  │\n", "│ 2025-01-21 00:00:00.000 ┆ 1438  │\n", "│ 2022-03-10 00:00:00.000 ┆ 1233  │\n", "│ 2018-03-16 00:00:00.000 ┆ 720   │\n", "│ 2025-01-28 00:00:00.000 ┆ 469   │\n", "│ 2019-02-12 00:00:00.000 ┆ 449   │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: <PERSON><PERSON>\n", "Unique values: 13735\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────┬───────┐\n", "│ Meter      ┆ count │\n", "│ ---        ┆ ---   │\n", "│ str        ┆ u32   │\n", "╞════════════╪═══════╡\n", "│ null       ┆ 35060 │\n", "│ KR6701234  ┆ 2     │\n", "│ NU57505941 ┆ 2     │\n", "│ NU57281468 ┆ 2     │\n", "│ EK5850325  ┆ 2     │\n", "│ GA5258962  ┆ 2     │\n", "│ GA5713594  ┆ 2     │\n", "│ GA3140250  ┆ 1     │\n", "│ EK6339714  ┆ 1     │\n", "│ EK5797933  ┆ 1     │\n", "└────────────┴───────┘\n", "\n", "Column: Social Security No_\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────┬───────┐\n", "│ Social Security No_ ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ null                ┆ 48800 │\n", "└─────────────────────┴───────┘\n", "\n", "Column: Cell Phone No_\n", "Unique values: 25939\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────────┬───────┐\n", "│ Cell Phone No_ ┆ count │\n", "│ ---            ┆ ---   │\n", "│ str            ┆ u32   │\n", "╞════════════════╪═══════╡\n", "│ null           ┆ 21518 │\n", "│ 967565441      ┆ 22    │\n", "│ 961378247      ┆ 14    │\n", "│ 914444333      ┆ 13    │\n", "│ 917326797      ┆ 12    │\n", "│ 964929265      ┆ 11    │\n", "│ 926241576      ┆ 11    │\n", "│ 961378460      ┆ 10    │\n", "│ 968484549      ┆ 9     │\n", "│ 918717154      ┆ 9     │\n", "└────────────────┴───────┘\n", "\n", "Column: Cust_ Portal Password\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────────────┬───────┐\n", "│ Cust_ Portal Password ┆ count │\n", "│ ---                   ┆ ---   │\n", "│ str                   ┆ u32   │\n", "╞═══════════════════════╪═══════╡\n", "│ null                  ┆ 48800 │\n", "└───────────────────────┴───────┘\n", "\n", "Column: Identification Doc_ Type Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────────────────────┬───────┐\n", "│ Identification Doc_ Type Code ┆ count │\n", "│ ---                           ┆ ---   │\n", "│ str                           ┆ u32   │\n", "╞═══════════════════════════════╪═══════╡\n", "│ null                          ┆ 48800 │\n", "└───────────────────────────────┴───────┘\n", "\n", "Column: Identification Doc_ No_\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Identification Doc_ No_ ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ null                    ┆ 48800 │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Preferred Contact Type Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────────┬───────┐\n", "│ Preferred Contact Type Code ┆ count │\n", "│ ---                         ┆ ---   │\n", "│ str                         ┆ u32   │\n", "╞═════════════════════════════╪═══════╡\n", "│ null                        ┆ 48800 │\n", "└─────────────────────────────┴───────┘\n", "\n", "Column: ID Doc_ Type Code ES\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌──────────────────────┬───────┐\n", "│ ID Doc_ Type Code ES ┆ count │\n", "│ ---                  ┆ ---   │\n", "│ str                  ┆ u32   │\n", "╞══════════════════════╪═══════╡\n", "│ null                 ┆ 48800 │\n", "└──────────────────────┴───────┘\n", "\n", "Column: ID Doc_ No_ ES\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────────┬───────┐\n", "│ ID Doc_ No_ ES ┆ count │\n", "│ ---            ┆ ---   │\n", "│ str            ┆ u32   │\n", "╞════════════════╪═══════╡\n", "│ null           ┆ 48800 │\n", "└────────────────┴───────┘\n", "\n", "Column: Agent No_\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────┬───────┐\n", "│ Agent No_ ┆ count │\n", "│ ---       ┆ ---   │\n", "│ str       ┆ u32   │\n", "╞═══════════╪═══════╡\n", "│ null      ┆ 48800 │\n", "└───────────┴───────┘\n", "\n", "Column: Creation Date\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Creation Date           ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 1753-01-01 00:00:00.000 ┆ 48800 │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Creation Source\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────┬───────┐\n", "│ Social Security No_ ┆ count │\n", "│ ---                 ┆ ---   │\n", "│ str                 ┆ u32   │\n", "╞═════════════════════╪═══════╡\n", "│ null                ┆ 48800 │\n", "└─────────────────────┴───────┘\n", "\n", "Column: Cell Phone No_\n", "Unique values: 25939\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌────────────────┬───────┐\n", "│ Cell Phone No_ ┆ count │\n", "│ ---            ┆ ---   │\n", "│ str            ┆ u32   │\n", "╞════════════════╪═══════╡\n", "│ null           ┆ 21518 │\n", "│ 967565441      ┆ 22    │\n", "│ 961378247      ┆ 14    │\n", "│ 914444333      ┆ 13    │\n", "│ 917326797      ┆ 12    │\n", "│ 964929265      ┆ 11    │\n", "│ 926241576      ┆ 11    │\n", "│ 961378460      ┆ 10    │\n", "│ 968484549      ┆ 9     │\n", "│ 918717154      ┆ 9     │\n", "└────────────────┴───────┘\n", "\n", "Column: Cust_ Portal Password\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────────────┬───────┐\n", "│ Cust_ Portal Password ┆ count │\n", "│ ---                   ┆ ---   │\n", "│ str                   ┆ u32   │\n", "╞═══════════════════════╪═══════╡\n", "│ null                  ┆ 48800 │\n", "└───────────────────────┴───────┘\n", "\n", "Column: Identification Doc_ Type Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────────────────────────┬───────┐\n", "│ Identification Doc_ Type Code ┆ count │\n", "│ ---                           ┆ ---   │\n", "│ str                           ┆ u32   │\n", "╞═══════════════════════════════╪═══════╡\n", "│ null                          ┆ 48800 │\n", "└───────────────────────────────┴───────┘\n", "\n", "Column: Identification Doc_ No_\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Identification Doc_ No_ ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ null                    ┆ 48800 │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Preferred Contact Type Code\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────────┬───────┐\n", "│ Preferred Contact Type Code ┆ count │\n", "│ ---                         ┆ ---   │\n", "│ str                         ┆ u32   │\n", "╞═════════════════════════════╪═══════╡\n", "│ null                        ┆ 48800 │\n", "└─────────────────────────────┴───────┘\n", "\n", "Column: ID Doc_ Type Code ES\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌──────────────────────┬───────┐\n", "│ ID Doc_ Type Code ES ┆ count │\n", "│ ---                  ┆ ---   │\n", "│ str                  ┆ u32   │\n", "╞══════════════════════╪═══════╡\n", "│ null                 ┆ 48800 │\n", "└──────────────────────┴───────┘\n", "\n", "Column: ID Doc_ No_ ES\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌────────────────┬───────┐\n", "│ ID Doc_ No_ ES ┆ count │\n", "│ ---            ┆ ---   │\n", "│ str            ┆ u32   │\n", "╞════════════════╪═══════╡\n", "│ null           ┆ 48800 │\n", "└────────────────┴───────┘\n", "\n", "Column: Agent No_\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌───────────┬───────┐\n", "│ Agent No_ ┆ count │\n", "│ ---       ┆ ---   │\n", "│ str       ┆ u32   │\n", "╞═══════════╪═══════╡\n", "│ null      ┆ 48800 │\n", "└───────────┴───────┘\n", "\n", "Column: Creation Date\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Creation Date           ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ 1753-01-01 00:00:00.000 ┆ 48800 │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Creation Source\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────┬───────┐\n", "│ Creation Source ┆ count │\n", "│ ---             ┆ ---   │\n", "│ str             ┆ u32   │\n", "╞═════════════════╪═══════╡\n", "│ null            ┆ 48800 │\n", "└─────────────────┴───────┘\n", "\n", "Column: Non-Paymt_ Periods Code\n", "Unique values: 32105\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Non-Paymt_ Periods Code ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ null                    ┆ 16659 │\n", "│ \\                       ┆ 13    │\n", "│ C                       ┆ 4     │\n", "│ T                       ┆ 3     │\n", "│ 70003806                ┆ 3     │\n", "│ 70004372                ┆ 2     │\n", "│ C39854                  ┆ 2     │\n", "│ N                       ┆ 2     │\n", "│ 70000307                ┆ 2     │\n", "│ 70007312                ┆ 2     │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Payment Days Code\n", "Unique values: 32105\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────────────┬───────┐\n", "│ Payment Days Code ┆ count │\n", "│ ---               ┆ ---   │\n", "│ str               ┆ u32   │\n", "╞═══════════════════╪═══════╡\n", "│ null              ┆ 16659 │\n", "│ \\                 ┆ 14    │\n", "│ C                 ┆ 4     │\n", "│ 70003806          ┆ 3     │\n", "│ T                 ┆ 3     │\n", "│ C25524            ┆ 2     │\n", "│ 50107068          ┆ 2     │\n", "│ 70006529          ┆ 2     │\n", "│ 85310             ┆ 2     │\n", "│ 70007313          ┆ 2     │\n", "└───────────────────┴───────┘\n", "\n", "Column: BP Statistic Code\n", "Unique values: 3\n", "Top 10 most frequent values:\n", "shape: (3, 2)\n", "┌───────────────────┬───────┐\n", "│ BP Statistic Code ┆ count │\n", "│ ---               ┆ ---   │\n", "│ str               ┆ u32   │\n", "╞═══════════════════╪═══════╡\n", "│ null              ┆ 48787 │\n", "│ A1010             ┆ 8     │\n", "│ D7190             ┆ 5     │\n", "└───────────────────┴───────┘\n", "\n", "Column: BP Debit Pos_ Statistic Code\n", "Unique values: 3\n", "Top 10 most frequent values:\n", "shape: (3, 2)\n", "┌──────────────────────────────┬───────┐\n", "│ BP Debit Pos_ Statistic Code ┆ count │\n", "│ ---                          ┆ ---   │\n", "│ str                          ┆ u32   │\n", "╞══════════════════════════════╪═══════╡\n", "│ null                         ┆ 48787 │\n", "│ N1011                        ┆ 12    │\n", "│ A1010                        ┆ 1     │\n", "└──────────────────────────────┴───────┘\n", "\n", "Column: BP Credit Pos_ Statistic Code\n", "Unique values: 2\n", "Top 10 most frequent values:\n", "shape: (2, 2)\n", "┌───────────────────────────────┬───────┐\n", "│ BP Credit Pos_ Statistic Code ┆ count │\n", "│ ---                           ┆ ---   │\n", "│ str                           ┆ u32   │\n", "╞═══════════════════════════════╪═══════╡\n", "│ null                          ┆ 48788 │\n", "│ N2011                         ┆ 12    │\n", "└───────────────────────────────┴───────┘\n", "\n", "Column: Income Type\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────┬───────┐\n", "│ Income Type ┆ count │\n", "│ ---         ┆ ---   │\n", "│ str         ┆ u32   │\n", "╞═════════════╪═══════╡\n", "│ null        ┆ 48800 │\n", "└─────────────┴───────┘\n", "\n", "Column: <PERSON><PERSON>\n", "Unique values: 15\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Email Broker                    ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 48784 │\n", "│ <EMAIL>    ┆ 2     │\n", "│ <EMAIL>     ┆ 2     │\n", "│ <EMAIL>             ┆ 1     │\n", "│ sarah.oshea@casasdobarlavento.… ┆ 1     │\n", "│ <EMAIL>                   ┆ 1     │\n", "│ <EMAIL>   ┆ 1     │\n", "│ <EMAIL>              ┆ 1     │\n", "│ <EMAIL>          ┆ 1     │\n", "│ <EMAIL>     ┆ 1     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: General E-Mail\n", "Unique values: 1549\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ General E-Mail                  ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 46468 │\n", "│ <EMAIL>;fatura… ┆ 270   │\n", "│ <EMAIL>       ┆ 68    │\n", "│ <EMAIL>        ┆ 54    │\n", "│ <EMAIL>             ┆ 25    │\n", "│ <EMAIL>;Joao.A… ┆ 14    │\n", "│ <EMAIL>          ┆ 14    │\n", "│ <EMAIL>;di@rhcast… ┆ 11    │\n", "│ <EMAIL>              ┆ 10    │\n", "│ <EMAIL>    ┆ 9     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "==================================================\n", "OUTLIER DETECTION (IQR METHOD)\n", "==================================================\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────────┬───────┐\n", "│ Creation Source ┆ count │\n", "│ ---             ┆ ---   │\n", "│ str             ┆ u32   │\n", "╞═════════════════╪═══════╡\n", "│ null            ┆ 48800 │\n", "└─────────────────┴───────┘\n", "\n", "Column: Non-Paymt_ Periods Code\n", "Unique values: 32105\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────┬───────┐\n", "│ Non-Paymt_ Periods Code ┆ count │\n", "│ ---                     ┆ ---   │\n", "│ str                     ┆ u32   │\n", "╞═════════════════════════╪═══════╡\n", "│ null                    ┆ 16659 │\n", "│ \\                       ┆ 13    │\n", "│ C                       ┆ 4     │\n", "│ T                       ┆ 3     │\n", "│ 70003806                ┆ 3     │\n", "│ 70004372                ┆ 2     │\n", "│ C39854                  ┆ 2     │\n", "│ N                       ┆ 2     │\n", "│ 70000307                ┆ 2     │\n", "│ 70007312                ┆ 2     │\n", "└─────────────────────────┴───────┘\n", "\n", "Column: Payment Days Code\n", "Unique values: 32105\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌───────────────────┬───────┐\n", "│ Payment Days Code ┆ count │\n", "│ ---               ┆ ---   │\n", "│ str               ┆ u32   │\n", "╞═══════════════════╪═══════╡\n", "│ null              ┆ 16659 │\n", "│ \\                 ┆ 14    │\n", "│ C                 ┆ 4     │\n", "│ 70003806          ┆ 3     │\n", "│ T                 ┆ 3     │\n", "│ C25524            ┆ 2     │\n", "│ 50107068          ┆ 2     │\n", "│ 70006529          ┆ 2     │\n", "│ 85310             ┆ 2     │\n", "│ 70007313          ┆ 2     │\n", "└───────────────────┴───────┘\n", "\n", "Column: BP Statistic Code\n", "Unique values: 3\n", "Top 10 most frequent values:\n", "shape: (3, 2)\n", "┌───────────────────┬───────┐\n", "│ BP Statistic Code ┆ count │\n", "│ ---               ┆ ---   │\n", "│ str               ┆ u32   │\n", "╞═══════════════════╪═══════╡\n", "│ null              ┆ 48787 │\n", "│ A1010             ┆ 8     │\n", "│ D7190             ┆ 5     │\n", "└───────────────────┴───────┘\n", "\n", "Column: BP Debit Pos_ Statistic Code\n", "Unique values: 3\n", "Top 10 most frequent values:\n", "shape: (3, 2)\n", "┌──────────────────────────────┬───────┐\n", "│ BP Debit Pos_ Statistic Code ┆ count │\n", "│ ---                          ┆ ---   │\n", "│ str                          ┆ u32   │\n", "╞══════════════════════════════╪═══════╡\n", "│ null                         ┆ 48787 │\n", "│ N1011                        ┆ 12    │\n", "│ A1010                        ┆ 1     │\n", "└──────────────────────────────┴───────┘\n", "\n", "Column: BP Credit Pos_ Statistic Code\n", "Unique values: 2\n", "Top 10 most frequent values:\n", "shape: (2, 2)\n", "┌───────────────────────────────┬───────┐\n", "│ BP Credit Pos_ Statistic Code ┆ count │\n", "│ ---                           ┆ ---   │\n", "│ str                           ┆ u32   │\n", "╞═══════════════════════════════╪═══════╡\n", "│ null                          ┆ 48788 │\n", "│ N2011                         ┆ 12    │\n", "└───────────────────────────────┴───────┘\n", "\n", "Column: Income Type\n", "Unique values: 1\n", "Top 10 most frequent values:\n", "shape: (1, 2)\n", "┌─────────────┬───────┐\n", "│ Income Type ┆ count │\n", "│ ---         ┆ ---   │\n", "│ str         ┆ u32   │\n", "╞═════════════╪═══════╡\n", "│ null        ┆ 48800 │\n", "└─────────────┴───────┘\n", "\n", "Column: <PERSON><PERSON>\n", "Unique values: 15\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ Email Broker                    ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 48784 │\n", "│ <EMAIL>    ┆ 2     │\n", "│ <EMAIL>     ┆ 2     │\n", "│ <EMAIL>             ┆ 1     │\n", "│ sarah.oshea@casasdobarlavento.… ┆ 1     │\n", "│ <EMAIL>                   ┆ 1     │\n", "│ <EMAIL>   ┆ 1     │\n", "│ <EMAIL>              ┆ 1     │\n", "│ <EMAIL>          ┆ 1     │\n", "│ <EMAIL>     ┆ 1     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "Column: General E-Mail\n", "Unique values: 1549\n", "Top 10 most frequent values:\n", "shape: (10, 2)\n", "┌─────────────────────────────────┬───────┐\n", "│ General E-Mail                  ┆ count │\n", "│ ---                             ┆ ---   │\n", "│ str                             ┆ u32   │\n", "╞═════════════════════════════════╪═══════╡\n", "│ null                            ┆ 46468 │\n", "│ <EMAIL>;fatura… ┆ 270   │\n", "│ <EMAIL>       ┆ 68    │\n", "│ <EMAIL>        ┆ 54    │\n", "│ <EMAIL>             ┆ 25    │\n", "│ <EMAIL>;Joao.A… ┆ 14    │\n", "│ <EMAIL>          ┆ 14    │\n", "│ <EMAIL>;di@rhcast… ┆ 11    │\n", "│ <EMAIL>              ┆ 10    │\n", "│ <EMAIL>    ┆ 9     │\n", "└─────────────────────────────────┴───────┘\n", "\n", "==================================================\n", "OUTLIER DETECTION (IQR METHOD)\n", "==================================================\n", "No_: 2274 outliers (4.66%)\n", "Telex No_: 0 outliers (0.00%)\n", "Global Dimension 1 Code: 28 outliers (0.06%)\n", "Global Dimension 2 Code: 57 outliers (0.12%)\n", "Budgeted Amount: 0 outliers (0.00%)\n", "Credit Limit (LCY): 3385 outliers (6.94%)\n", "Statistics Group: 0 outliers (0.00%)\n", "No_: 2274 outliers (4.66%)\n", "Telex No_: 0 outliers (0.00%)\n", "Global Dimension 1 Code: 28 outliers (0.06%)\n", "Global Dimension 2 Code: 57 outliers (0.12%)\n", "Budgeted Amount: 0 outliers (0.00%)\n", "Credit Limit (LCY): 3385 outliers (6.94%)\n", "Statistics Group: 0 outliers (0.00%)\n", "Salesperson Code: 555 outliers (1.14%)\n", "Shipment Method Code: 77 outliers (0.16%)\n", "Invoice Disc_ Code: 2278 outliers (4.67%)\n", "Amount: 0 outliers (0.00%)\n", "Blocked: 2 outliers (0.00%)\n", "Invoice Copies: 45 outliers (0.09%)\n", "Last Statement No_: 7598 outliers (15.57%)\n", "Salesperson Code: 555 outliers (1.14%)\n", "Shipment Method Code: 77 outliers (0.16%)\n", "Invoice Disc_ Code: 2278 outliers (4.67%)\n", "Amount: 0 outliers (0.00%)\n", "Blocked: 2 outliers (0.00%)\n", "Invoice Copies: 45 outliers (0.09%)\n", "Last Statement No_: 7598 outliers (15.57%)\n", "Print Statements: 7 outliers (0.01%)\n", "Bill-to Customer No_: 748 outliers (1.53%)\n", "Priority: 0 outliers (0.00%)\n", "Application Method: 0 outliers (0.00%)\n", "Prices Including VAT: 5 outliers (0.01%)\n", "Location Code: 535 outliers (1.10%)\n", "VAT Registration No_: 1018 outliers (2.09%)\n", "Print Statements: 7 outliers (0.01%)\n", "Bill-to Customer No_: 748 outliers (1.53%)\n", "Priority: 0 outliers (0.00%)\n", "Application Method: 0 outliers (0.00%)\n", "Prices Including VAT: 5 outliers (0.01%)\n", "Location Code: 535 outliers (1.10%)\n", "VAT Registration No_: 1018 outliers (2.09%)\n", "Combine Shipments: 10 outliers (0.02%)\n", "Tax Liable: 0 outliers (0.00%)\n", "Reserve: 7 outliers (0.01%)\n", "Block Payment Tolerance: 3 outliers (0.01%)\n", "Prepayment _: 0 outliers (0.00%)\n", "Partner Type: 356 outliers (0.73%)\n", "Combine Shipments: 10 outliers (0.02%)\n", "Tax Liable: 0 outliers (0.00%)\n", "Reserve: 7 outliers (0.01%)\n", "Block Payment Tolerance: 3 outliers (0.01%)\n", "Prepayment _: 0 outliers (0.00%)\n", "Partner Type: 356 outliers (0.73%)\n", "Shipping Advice: 1 outliers (0.00%)\n", "Allow Line Disc_: 19 outliers (0.04%)\n", "Copy <PERSON>ll-to Addr_ to Qte From: 9 outliers (0.02%)\n", "Master Customer No_: 0 outliers (0.00%)\n", "Master: 11721 outliers (24.02%)\n", "CAE Code: 9998 outliers (20.49%)\n", "Shipping Advice: 1 outliers (0.00%)\n", "Allow Line Disc_: 19 outliers (0.04%)\n", "Copy <PERSON>ll-to Addr_ to Qte From: 9 outliers (0.02%)\n", "Master Customer No_: 0 outliers (0.00%)\n", "Master: 11721 outliers (24.02%)\n", "CAE Code: 9998 outliers (20.49%)\n", "GPS Latitude: 1 outliers (0.00%)\n", "Generate Orders: 1786 outliers (3.66%)\n", "Area Manager: 16379 outliers (33.56%)\n", "Debit Bank: 0 outliers (0.00%)\n", "Bank Identification No_: 46 outliers (0.09%)\n", "Debit Authorization Bank No_: 56 outliers (0.11%)\n", "GPS Latitude: 1 outliers (0.00%)\n", "Generate Orders: 1786 outliers (3.66%)\n", "Area Manager: 16379 outliers (33.56%)\n", "Debit Bank: 0 outliers (0.00%)\n", "Bank Identification No_: 46 outliers (0.09%)\n", "Debit Authorization Bank No_: 56 outliers (0.11%)\n", "ADC Maximum Value: 0 outliers (0.00%)\n", "Valid ADC: 0 outliers (0.00%)\n", "No_ of Direct Debit Rejections: 0 outliers (0.00%)\n", "Bin Code: 26 outliers (0.05%)\n", "unloadTimeFixed: 1 outliers (0.00%)\n", "unloadTimeVariablePump: 2 outliers (0.00%)\n", "ADC Maximum Value: 0 outliers (0.00%)\n", "Valid ADC: 0 outliers (0.00%)\n", "No_ of Direct Debit Rejections: 0 outliers (0.00%)\n", "Bin Code: 26 outliers (0.05%)\n", "unloadTimeFixed: 1 outliers (0.00%)\n", "unloadTimeVariablePump: 2 outliers (0.00%)\n", "unloadTimeVariableGravity: 0 outliers (0.00%)\n", "unloadTimeVariableHose: 0 outliers (0.00%)\n", "Created First Orion File: 7779 outliers (15.94%)\n", "ISP Free: 16 outliers (0.03%)\n", "Bill-to: 11526 outliers (23.62%)\n", "Type: 0 outliers (0.00%)\n", "unloadTimeVariableGravity: 0 outliers (0.00%)\n", "unloadTimeVariableHose: 0 outliers (0.00%)\n", "Created First Orion File: 7779 outliers (15.94%)\n", "ISP Free: 16 outliers (0.03%)\n", "Bill-to: 11526 outliers (23.62%)\n", "Type: 0 outliers (0.00%)\n", "Invoice: 5043 outliers (10.33%)\n", "Classification: 0 outliers (0.00%)\n", "Status: 338 outliers (0.69%)\n", "Send Invoice E-Mail: 8 outliers (0.02%)\n", "Invoice Combined Day: 32 outliers (0.07%)\n", "Annual Volume: 3301 outliers (6.76%)\n", "Total Volume: 3324 outliers (6.81%)\n", "Invoice: 5043 outliers (10.33%)\n", "Classification: 0 outliers (0.00%)\n", "Status: 338 outliers (0.69%)\n", "Send Invoice E-Mail: 8 outliers (0.02%)\n", "Invoice Combined Day: 32 outliers (0.07%)\n", "Annual Volume: 3301 outliers (6.76%)\n", "Total Volume: 3324 outliers (6.81%)\n", "Ship-to: 8432 outliers (17.28%)\n", "Shortcut Dimension 3: 1950 outliers (4.00%)\n", "Shortcut Dimension 4: 1397 outliers (2.86%)\n", "Shortcut Dimension 5: 16219 outliers (33.24%)\n", "Shortcut Dimension 8: 10277 outliers (21.06%)\n", "Send Table Regular Prices: 111 outliers (0.23%)\n", "Ship-to: 8432 outliers (17.28%)\n", "Shortcut Dimension 3: 1950 outliers (4.00%)\n", "Shortcut Dimension 4: 1397 outliers (2.86%)\n", "Shortcut Dimension 5: 16219 outliers (33.24%)\n", "Shortcut Dimension 8: 10277 outliers (21.06%)\n", "Send Table Regular Prices: 111 outliers (0.23%)\n", "Send Table K Prices: 0 outliers (0.00%)\n", "Send Email Daily Deliveries: 0 outliers (0.00%)\n", "ISP Tax: 0 outliers (0.00%)\n", "IsCustomer: 27 outliers (0.06%)\n", "IsDelivery: 27 outliers (0.06%)\n", "IsBilling: 27 outliers (0.06%)\n", "IsOrderTaker: 27 outliers (0.06%)\n", "Send Table K Prices: 0 outliers (0.00%)\n", "Send Email Daily Deliveries: 0 outliers (0.00%)\n", "ISP Tax: 0 outliers (0.00%)\n", "IsCustomer: 27 outliers (0.06%)\n", "IsDelivery: 27 outliers (0.06%)\n", "IsBilling: 27 outliers (0.06%)\n", "IsOrderTaker: 27 outliers (0.06%)\n", "IsRegistered: 6 outliers (0.01%)\n", "Classification GESGAS: 0 outliers (0.00%)\n", "Not National Taxable Person: 11 outliers (0.02%)\n", "Registered Cust_ Portal: 0 outliers (0.00%)\n", "Block Payment Agreement: 0 outliers (0.00%)\n", "Customer Type ES: 0 outliers (0.00%)\n", "Utilities Doc_ Copies: 0 outliers (0.00%)\n", "IsRegistered: 6 outliers (0.01%)\n", "Classification GESGAS: 0 outliers (0.00%)\n", "Not National Taxable Person: 11 outliers (0.02%)\n", "Registered Cust_ Portal: 0 outliers (0.00%)\n", "Block Payment Agreement: 0 outliers (0.00%)\n", "Customer Type ES: 0 outliers (0.00%)\n", "Utilities Doc_ Copies: 0 outliers (0.00%)\n", "Cash VAT Customer: 116 outliers (0.24%)\n", "Create Receipt: 208 outliers (0.43%)\n", "Default Bank Acc_ Code: 613 outliers (1.26%)\n", "Subject to Withholding Tax: 30 outliers (0.06%)\n", "Send Email Warnings and NC: 5162 outliers (10.58%)\n", "End Consumer: 7 outliers (0.01%)\n", "Cash VAT Customer: 116 outliers (0.24%)\n", "Create Receipt: 208 outliers (0.43%)\n", "Default Bank Acc_ Code: 613 outliers (1.26%)\n", "Subject to Withholding Tax: 30 outliers (0.06%)\n", "Send Email Warnings and NC: 5162 outliers (10.58%)\n", "End Consumer: 7 outliers (0.01%)\n", "Zone: 1 outliers (0.00%)\n", "Generix Web Service Type: 0 outliers (0.00%)\n", "Completed Car: 150 outliers (0.31%)\n", "Completed Container: 228 outliers (0.47%)\n", "Containers Qty: 200 outliers (0.41%)\n", "Sales Delegate: 0 outliers (0.00%)\n", "\n", "==================================================\n", "CORRELATION ANALYSIS\n", "==================================================\n", "Zone: 1 outliers (0.00%)\n", "Generix Web Service Type: 0 outliers (0.00%)\n", "Completed Car: 150 outliers (0.31%)\n", "Completed Container: 228 outliers (0.47%)\n", "Containers Qty: 200 outliers (0.41%)\n", "Sales Delegate: 0 outliers (0.00%)\n", "\n", "==================================================\n", "CORRELATION ANALYSIS\n", "==================================================\n", "shape: (90, 91)\n", "┌────────────┬─────┬───────────┬────────────┬───┬────────────┬────────────┬────────────┬───────────┐\n", "│ column     ┆ No_ ┆ Telex No_ ┆ Global     ┆ … ┆ Completed  ┆ Completed  ┆ Containers ┆ Sales     │\n", "│ ---        ┆ --- ┆ ---       ┆ Dimension  ┆   ┆ Car        ┆ Container  ┆ Qty        ┆ Delegate  │\n", "│ str        ┆ f64 ┆ f64       ┆ 1 Code     ┆   ┆ ---        ┆ ---        ┆ ---        ┆ ---       │\n", "│            ┆     ┆           ┆ ---        ┆   ┆ f64        ┆ f64        ┆ f64        ┆ f64       │\n", "│            ┆     ┆           ┆ f64        ┆   ┆            ┆            ┆            ┆           │\n", "╞════════════╪═════╪═══════════╪════════════╪═══╪════════════╪════════════╪════════════╪═══════════╡\n", "│ No_        ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Telex No_  ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Global     ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Dimension  ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ 1 Code     ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Global     ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Dimension  ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ 2 Code     ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Budgeted   ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Amount     ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ …          ┆ …   ┆ …         ┆ …          ┆ … ┆ …          ┆ …          ┆ …          ┆ …         │\n", "│ Generix    ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Web        ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Service    ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Type       ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Completed  ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ 1.0        ┆ 0.783315   ┆ 0.593949   ┆ NaN       │\n", "│ Car        ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Completed  ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ 0.783315   ┆ 1.0        ┆ 0.814526   ┆ NaN       │\n", "│ Container  ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Containers ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ 0.593949   ┆ 0.814526   ┆ 1.0        ┆ NaN       │\n", "│ Qty        ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Sales      ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Delegate   ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "└────────────┴─────┴───────────┴────────────┴───┴────────────┴────────────┴────────────┴───────────┘\n", "\n", "==================================================\n", "DATA QUALITY CHECK\n", "==================================================\n", "shape: (90, 91)\n", "┌────────────┬─────┬───────────┬────────────┬───┬────────────┬────────────┬────────────┬───────────┐\n", "│ column     ┆ No_ ┆ Telex No_ ┆ Global     ┆ … ┆ Completed  ┆ Completed  ┆ Containers ┆ Sales     │\n", "│ ---        ┆ --- ┆ ---       ┆ Dimension  ┆   ┆ Car        ┆ Container  ┆ Qty        ┆ Delegate  │\n", "│ str        ┆ f64 ┆ f64       ┆ 1 Code     ┆   ┆ ---        ┆ ---        ┆ ---        ┆ ---       │\n", "│            ┆     ┆           ┆ ---        ┆   ┆ f64        ┆ f64        ┆ f64        ┆ f64       │\n", "│            ┆     ┆           ┆ f64        ┆   ┆            ┆            ┆            ┆           │\n", "╞════════════╪═════╪═══════════╪════════════╪═══╪════════════╪════════════╪════════════╪═══════════╡\n", "│ No_        ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Telex No_  ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Global     ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Dimension  ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ 1 Code     ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Global     ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Dimension  ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ 2 Code     ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Budgeted   ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Amount     ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ …          ┆ …   ┆ …         ┆ …          ┆ … ┆ …          ┆ …          ┆ …          ┆ …         │\n", "│ Generix    ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Web        ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Service    ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Type       ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Completed  ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ 1.0        ┆ 0.783315   ┆ 0.593949   ┆ NaN       │\n", "│ Car        ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Completed  ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ 0.783315   ┆ 1.0        ┆ 0.814526   ┆ NaN       │\n", "│ Container  ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Containers ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ 0.593949   ┆ 0.814526   ┆ 1.0        ┆ NaN       │\n", "│ Qty        ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "│ Sales      ┆ NaN ┆ NaN       ┆ NaN        ┆ … ┆ NaN        ┆ NaN        ┆ NaN        ┆ NaN       │\n", "│ Delegate   ┆     ┆           ┆            ┆   ┆            ┆            ┆            ┆           │\n", "└────────────┴─────┴───────────┴────────────┴───┴────────────┴────────────┴────────────┴───────────┘\n", "\n", "==================================================\n", "DATA QUALITY CHECK\n", "==================================================\n", "Duplicate rows: 0\n", "Constant columns: ['Our Account No_', 'Territory Code', 'Chain Name', 'Budgeted Amount', 'Statistics Group', 'Fin_ Charge Terms Code', 'Shipping Agent Code', 'Place of Export', 'Collection Method', 'Amount', 'Priority', 'Application Method', 'Telex Answer Back', 'Picture', 'GLN', 'Tax Area Code', 'Tax Liable', 'IC Partner Code', 'Prepayment _', 'Image', 'Preferred Bank Account Code', 'Responsibility Center', 'Shipping Time', 'Shipping Agent Service Code', 'Base Calendar Code', 'Commission Agent', 'Debit Bank', 'ADC Maximum Value', 'ADC Limit Date', 'Valid ADC', 'Old ADC', 'No_ of Direct Debit Rejections', 'unloadTimeVariableGravity', 'unloadTimeVariableHose', 'Send Table K Prices', 'E-Mail Precos', 'Send Email Daily Deliveries', 'Email Daily Deliveries', 'E-Mail GR', 'Classification GESGAS', 'Social Security No_', 'Registered Cust_ Portal', 'Cust_ Portal Password', 'Block Payment Agreement', 'Identification Doc_ Type Code', 'Identification Doc_ No_', 'Preferred Contact Type Code', 'ID Doc_ Type Code ES', 'ID Doc_ No_ ES', 'Customer Type ES', 'Agent No_', 'Creation Date', 'Creation Source', 'Utilities Doc_ Copies', 'Income Type', 'Generix Web Service Type']\n", "High cardinality categorical columns: ['Name', 'Search Name', 'Address', 'Cell Phone No_', 'Non-Paymt_ Periods Code', 'Payment Days Code']\n", "\n", "============================================================\n", "✅ EDA Complete!\n", "Duplicate rows: 0\n", "Constant columns: ['Our Account No_', 'Territory Code', 'Chain Name', 'Budgeted Amount', 'Statistics Group', 'Fin_ Charge Terms Code', 'Shipping Agent Code', 'Place of Export', 'Collection Method', 'Amount', 'Priority', 'Application Method', 'Telex Answer Back', 'Picture', 'GLN', 'Tax Area Code', 'Tax Liable', 'IC Partner Code', 'Prepayment _', 'Image', 'Preferred Bank Account Code', 'Responsibility Center', 'Shipping Time', 'Shipping Agent Service Code', 'Base Calendar Code', 'Commission Agent', 'Debit Bank', 'ADC Maximum Value', 'ADC Limit Date', 'Valid ADC', 'Old ADC', 'No_ of Direct Debit Rejections', 'unloadTimeVariableGravity', 'unloadTimeVariableHose', 'Send Table K Prices', 'E-Mail Precos', 'Send Email Daily Deliveries', 'Email Daily Deliveries', 'E-Mail GR', 'Classification GESGAS', 'Social Security No_', 'Registered Cust_ Portal', 'Cust_ Portal Password', 'Block Payment Agreement', 'Identification Doc_ Type Code', 'Identification Doc_ No_', 'Preferred Contact Type Code', 'ID Doc_ Type Code ES', 'ID Doc_ No_ ES', 'Customer Type ES', 'Agent No_', 'Creation Date', 'Creation Source', 'Utilities Doc_ Copies', 'Income Type', 'Generix Web Service Type']\n", "High cardinality categorical columns: ['Name', 'Search Name', 'Address', 'Cell Phone No_', 'Non-Paymt_ Periods Code', 'Payment Days Code']\n", "\n", "============================================================\n", "✅ EDA Complete!\n"]}], "source": ["# Run EDA\n", "df = pl.read_csv(\n", "    \"__vREP_Customer__202507161242.csv\",\n", "    infer_schema_length=10000,\n", "    ignore_errors=True,\n", "    null_values=[\"\", \"NULL\", \"null\", \"N/A\", \"n/a\", \"NA\", \"P\", \"#N/A\", \"#NULL!\"]\n", ")\n", "\n", "eda = PolarEDA(df)\n", "eda.run_full_eda(generate_plots=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "45df80b8", "metadata": {}, "outputs": [], "source": ["# Status options:\n", "#  0 - <PERSON><PERSON>\n", "#  1 - <PERSON>\n", "#  2 - <PERSON><PERSON>-<PERSON>\n", "#  3 - Com Plano de Pagamentos\n", "#  4 - <PERSON><PERSON>-<PERSON>\n", "#  5 - Inativo\n", "#  6 - Inativo com tanque\n", "#  7 - Pré-Ativo\n", "#  8 - Inativo com Saldo\n", "#  9 - <PERSON><PERSON><PERSON><PERSON>\n", "# 10 - PER - Com Plano de Pagamentos\n", "# 11 - Ativo com Baixo Consumo\n", "# 12 - Com Plano de Pagamentos Judicial\n", "\n", "# Type options:\n", "#  0 - (Empty)\n", "#  1 - <PERSON><PERSON>\n", "#  2 - <PERSON><PERSON><PERSON>\n", "#  3 - <PERSON><PERSON><PERSON>\n", "#  4 - <PERSON><PERSON><PERSON>\n", "#  5 - Supply\n", "#  6 - <PERSON><PERSON>\n", "\n", "df_inactive = df.filter(\n", "    pl.col(\"Classification\").is_in([5,6,8]))\n", "\n", "df_active = df.filter(\n", "    ~pl.col(\"Classification\").is_in([5,6,8]))\n", "\n", "df_active_empty = df_active.filter(\n", "    pl.col(\"Type\") == 0)\n", "df_active_bulk = df_active.filter(\n", "    pl.col(\"Type\") == 1)\n", "df_active_bottled = df_active.filter(\n", "    pl.col(\"Type\") == 2)\n", "df_active_pipped = df_active.filter(\n", "    pl.col(\"Type\") == 3)\n", "df_active_supply = df_active.filter(\n", "    pl.col(\"Type\") == 5)\n", "df_active_other = df_active.filter(\n", "    pl.col(\"Type\") == 6)\n", "\n", "df_inactive_empty = df_inactive.filter(\n", "    pl.col(\"Type\") == 0)\n", "df_inactive_bulk = df_inactive.filter(\n", "    pl.col(\"Type\") == 1)\n", "df_inactive_bottled = df_inactive.filter(\n", "    pl.col(\"Type\") == 2)\n", "df_inactive_pipped = df_inactive.filter(\n", "    pl.col(\"Type\") == 3)\n", "df_inactive_supply = df_inactive.filter(\n", "    pl.col(\"Type\") == 5)\n", "df_inactive_other = df_inactive.filter(\n", "    pl.col(\"Type\") == 6)"]}, {"cell_type": "code", "execution_count": null, "id": "81bf05dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Active & Empty: 55\n", "Active & Bulk: 6808\n", "Active & Bottled: 336\n", "Active & Pipped: 16897\n", "Active & Supply: 28\n", "Active & Other: 48\n", "==============================\n", "Inactive & Empty: 17\n", "Inactive & Bulk: 8645\n", "Inactive & Bottled: 360\n", "Inactive & Pipped: 15551\n", "Inactive & Supply: 55\n", "Inactive & Other: 0\n"]}], "source": ["# Count Active\n", "print(\"Active & Empty:\", df_active_empty.height)\n", "print(\"Active & Bulk:\", df_active_bulk.height)\n", "print(\"Active & Bottled:\", df_active_bottled.height)\n", "print(\"Active & Pipped:\", df_active_pipped.height)\n", "print(\"Active & Supply:\", df_active_supply.height)\n", "print(\"Active & Other:\", df_active_other.height)\n", "print(\"=\" * 30)\n", "# Count Inactive\n", "print(\"Inactive & Empty:\", df_inactive_empty.height)\n", "print(\"Inactive & Bulk:\", df_inactive_bulk.height)\n", "print(\"Inactive & Bottled:\", df_inactive_bottled.height)\n", "print(\"Inactive & Pipped:\", df_inactive_pipped.height)\n", "print(\"Inactive & Supply:\", df_inactive_supply.height)\n", "print(\"Inactive & Other:\", df_inactive_other.height)"]}, {"cell_type": "code", "execution_count": null, "id": "17b04912", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1761\n", "Existing Vendors (Excluding Employees): 1536\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["df_vendor = pl.read_csv(\n", "    \"__vREP_Vendor__202507161257.csv\",\n", "    infer_schema_length=10000,\n", "    ignore_errors=True,\n", "    null_values=[\"\", \"NULL\", \"null\", \"N/A\", \"n/a\", \"NA\", \"P\", \"#N/A\", \"#NULL!\"]\n", ")\n", "\n", "print(df_vendor.height)\n", "\n", "df_vendor = df_vendor.filter(~pl.col(\"No_\").str.starts_with(\"E\"))\n", "\n", "print(\"Existing Vendors (Excluding Employees):\", df_vendor.height)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}